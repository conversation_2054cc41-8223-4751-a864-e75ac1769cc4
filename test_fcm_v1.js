const https = require('https');
const crypto = require('crypto');

// Your FCM Token
const FCM_TOKEN = "dW5jfBcDT5OYiLwwa9kTOQ:APA91bF_mxEhW2jcS-mdCLWfCdbPrFMmvl3TwzCPKENcvEnRhmYyB_bNr6adjKlOgj6UhZyd87AxXhwYhSxVv7kX22OgXD_vZvotCmKw57LqiBHIKW5tr4A";

// Service Account Configuration
const SERVICE_ACCOUNT = {
  "type": "service_account",
  "project_id": "dalti-prod",
  "private_key": "-----B<PERSON>IN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCmPAhimjq+NqOx\n3tKbnaIUURs0HhPUzIuG+zXbT5c73i5r09fIC9hPkyxk+eSP2JcNnO9DiWn2m+jr\nA+C+Ul6ls7Ykd5TO3wAvxdlRW5vVozegva4IaaoAuu0WCub2XEMm1eCeNL4HmOv6\njT/w7lFxCQvoJHdBNn2WtlUqi8If5lDTBaCLkhYzuZaEqWtKFH6+jugfNLMbCytV\nvJuMOcR8WCc5ulbiqF3rd2F40lD4iVRfEWG5EQncrYcBC4/2pZO+T3Y4TK/xdfmN\n1ziPGmKf5WXNzxVHtWBy6ubjzYIQj5TH6HJaOpd9+EzGMWS/DUEOca/X7RH3qysB\nUPaek7jZAgMBAAECggEAFe0kh1T5MJkRExIP94Sy7zkaJyVcekdsk/uonpUrSTNE\n2OYoDR6QLpbRVpRQ3dm0uuVF+JjfHYRyaXqpIebIIypxePKTnIx01YL5ki33f8C3\n7VTM6BZ/3Pl1nOBdQduTjpjMDYdpaqPKDydNwCAUep4LMFiMU6a1/rNtcl+IKyAa\n1BMftcSTr6TdhtbEBNYW4T2fWR5VoP5b6mhpRdstD7DLNdhEs4yC4nGFjvbZML1a\nrfETK7oL43LHv83HWPf0scVkcrKBkRTKyPMtpWh6sV5FYFdMOtpyAQTaCrDsX+9l\nUD0f50oWUqjgHdcm0SUIfRvOb9Axrdu+EG6nPU2RHQKBgQDm689811dTpYK5Fl+9\n4T1CoJXNe7n/AZmbioVKq9KzhUPCgW/xHHI1/tIXmVvHw4HT0sWigFtkx1rGlIn+\nUZkth/RGdeYj0s9Kor2dottS72Efrna6ufQIyOmAljvnceEQ/+57CJNcE+F8Qf9x\neQ5nv+1oe3lJr77obztHl+OJWwKBgQC4ScVX2VaVnQ4C4ezCoqfM5Nnc3q3EUY3Y\ns9ISjJ51TdDVz0b5xktJvu2bmGUv3x7WBvo5WU8ZrADPxEnW3KfvqZ/MKYfMAtKE\nYSK/lBRVhAvWkze4mI30hRH7asUcrCIz1QOB+RNjCV8KrahhSx0Jg+7bJm+coJEy\nZ9o/JxUo2wKBgQDZie5mckrvHu2+RWiebVETskpNULCQncZCdEVLagDNY87Irr1x\nC9ZALbVny+5diz2D+nx6sKbokrFef9Jbxi0iaoBh3HGY7+CeNB+jICezwpThjq6F\nC+bsW7E1lNIMIAjhxei4+QQxav0x3M6y7FL4xCL4GdHR7AFR4G+c62bMHwKBgCZa\n9D+8dFDfRzNCYkyAHfx/BPj7P59EeglII8jIi5JOh9B4O4Vwx+qpWjqwFR9JiNly\nylF1TQlCy0hyygt2EV3IKAIOAr1rOPVkYh8Nas4BlZUrPsWkuqbRq1RwfGXjTZsM\nEpbSiD5sjONkEU9umrSISQZDkac/o/ihtMLAF59RAoGACgMQ1BlKq9uv9j6YHhlT\nahnM+dTn72gWJy0hBX6D4Hf+pRo7vo4KTSB1tsD8DWNNKNVje0yZz0CQDFG9Hvma\nPDR4TlFKQ3K40/vRlHF0wf/w33KP9NmMZITZKiMFDeMZgvTfA98D/xscT43Hft1G\n+vBdGoUesoHjfqB6EKaLwW0=\n-----END PRIVATE KEY-----",
  "client_email": "<EMAIL>"
};

// Simple JWT creation without external libraries
function createJWT() {
  const header = {
    "alg": "RS256",
    "typ": "JWT"
  };

  const now = Math.floor(Date.now() / 1000);
  const payload = {
    "iss": SERVICE_ACCOUNT.client_email,
    "scope": "https://www.googleapis.com/auth/firebase.messaging",
    "aud": "https://oauth2.googleapis.com/token",
    "iat": now,
    "exp": now + 3600
  };

  const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url');
  const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64url');
  
  const signatureInput = `${encodedHeader}.${encodedPayload}`;
  const signature = crypto.sign('RSA-SHA256', Buffer.from(signatureInput), SERVICE_ACCOUNT.private_key);
  const encodedSignature = signature.toString('base64url');
  
  return `${signatureInput}.${encodedSignature}`;
}

// Get OAuth2 access token
function getAccessToken() {
  return new Promise((resolve, reject) => {
    const jwt = createJWT();
    const postData = `grant_type=urn:ietf:params:oauth:grant-type:jwt-bearer&assertion=${jwt}`;
    
    const options = {
      hostname: 'oauth2.googleapis.com',
      port: 443,
      path: '/token',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (response.access_token) {
            resolve(response.access_token);
          } else {
            reject(new Error('No access token in response: ' + data));
          }
        } catch (error) {
          reject(error);
        }
      });
    });
    
    req.on('error', reject);
    req.write(postData);
    req.end();
  });
}

// Send FCM notification using HTTP v1 API
async function sendNotification() {
  try {
    console.log('🔥 Firebase FCM HTTP v1 API Test');
    console.log('📱 Target Token:', FCM_TOKEN.substring(0, 30) + '...');
    console.log('🎯 Project: dalti-prod');
    console.log('');
    
    console.log('🔑 Getting OAuth2 access token...');
    const accessToken = await getAccessToken();
    console.log('✅ Access token obtained');
    
    const message = {
      message: {
        token: FCM_TOKEN,
        notification: {
          title: "🚀 HTTP v1 API Test",
          body: "Hello! This notification was sent using Firebase HTTP v1 API"
        },
        data: {
          test_key: "test_value",
          timestamp: new Date().toISOString(),
          source: "http_v1_api"
        },
        android: {
          priority: "high",
          notification: {
            channel_id: "dalti_provider_notifications",
            icon: "launcher_icon",
            color: "#15424E"
          }
        }
      }
    };
    
    const postData = JSON.stringify(message);
    
    const options = {
      hostname: 'fcm.googleapis.com',
      port: 443,
      path: '/v1/projects/dalti-prod/messages:send',
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    console.log('📤 Sending notification via HTTP v1 API...');
    
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        console.log(`📊 Response Status: ${res.statusCode}`);
        console.log('📄 Response Body:', data);
        
        if (res.statusCode === 200) {
          console.log('🎉 SUCCESS! Notification sent successfully!');
          console.log('👀 Check your app now - you should see the notification!');
          console.log('📱 Make sure your app is open to see FCM logs in console');
        } else {
          console.log('❌ FAILED! Error sending notification');
        }
      });
    });
    
    req.on('error', (error) => {
      console.error('❌ Request error:', error);
    });
    
    req.write(postData);
    req.end();
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Run the test
sendNotification();
