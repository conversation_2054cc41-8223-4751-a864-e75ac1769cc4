
const { onRequest } = require('firebase-functions/v2/https');
const admin = require('firebase-admin');

// Initialize Firebase Admin
admin.initializeApp();

exports.testFCM = onRequest(async (req, res) => {
    try {
        console.log('🚀 FCM Test Function called');
        
        const message = {
            token: 'dX8_yF2uRyyVvcN-AmfjFl:APA91bFuCLThVntBVy-OHA_w12zbh1T7XYXcdV5nAKlxC1VRrFYfrMfHc5C3NjnhBIVW2ccv-QIBumqGpX5yMGQcgtuRNXp9RY3dJjK5mWGWKLYH9DtAuIM',
            notification: {
                title: '🎉 Firebase Function Test',
                body: 'Success! FCM working via Cloud Function'
            },
            data: {
                test_type: 'cloud_function',
                timestamp: new Date().toISOString(),
                source: 'firebase_function'
            },
            android: {
                priority: 'high',
                notification: {
                    channelId: 'dalti_provider_notifications',
                    icon: 'launcher_icon',
                    color: '#15424E',
                    sound: 'default'
                }
            }
        };
        
        console.log('📤 Sending FCM message:', JSON.stringify(message, null, 2));
        
        const response = await admin.messaging().send(message);
        
        console.log('✅ FCM message sent successfully:', response);
        
        res.json({
            success: true,
            messageId: response,
            message: 'FCM notification sent successfully!',
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        console.error('❌ FCM Error:', error);
        
        res.status(500).json({
            success: false,
            error: error.message,
            code: error.code,
            timestamp: new Date().toISOString()
        });
    }
});
