{"roots": ["dalti_provider"], "packages": [{"name": "dalti_provider", "version": "1.0.0+1", "dependencies": ["cupertino_icons", "dio", "equatable", "file_picker", "firebase_core", "firebase_messaging", "flutter", "flutter_local_notifications", "flutter_localizations", "flutter_riverpod", "flutter_secure_storage", "freezed_annotation", "geolocator", "go_router", "google_fonts", "hive", "hive_flutter", "http", "intl", "json_annotation", "mime", "mobile_scanner", "path", "path_provider", "phone_numbers_parser", "riverpod", "riverpod_annotation", "shared_preferences", "table_calendar", "url_launcher", "web_socket_channel"], "devDependencies": ["build_runner", "custom_lint", "flutter_launcher_icons", "flutter_lints", "flutter_test", "freezed", "json_serializable", "mocktail", "riverpod_generator", "riverpod_lint"]}, {"name": "flutter_launcher_icons", "version": "0.14.4", "dependencies": ["args", "checked_yaml", "cli_util", "image", "json_annotation", "path", "yaml"]}, {"name": "riverpod_lint", "version": "2.3.10", "dependencies": ["analyzer", "analyzer_plugin", "collection", "custom_lint_builder", "meta", "path", "riverpod", "riverpod_analyzer_utils", "source_span", "yaml"]}, {"name": "custom_lint", "version": "0.6.4", "dependencies": ["analyzer", "analyzer_plugin", "args", "async", "ci", "cli_util", "collection", "custom_lint_core", "freezed_annotation", "json_annotation", "meta", "package_config", "path", "pub_semver", "pubspec_parse", "rxdart", "uuid", "yaml"]}, {"name": "mocktail", "version": "1.0.4", "dependencies": ["collection", "matcher", "test_api"]}, {"name": "freezed", "version": "2.5.2", "dependencies": ["analyzer", "build", "build_config", "collection", "freezed_annotation", "json_annotation", "meta", "source_gen"]}, {"name": "json_serializable", "version": "6.8.0", "dependencies": ["analyzer", "async", "build", "build_config", "collection", "json_annotation", "meta", "path", "pub_semver", "pubspec_parse", "source_gen", "source_helper"]}, {"name": "build_runner", "version": "2.4.13", "dependencies": ["analyzer", "args", "async", "build", "build_config", "build_daemon", "build_resolvers", "build_runner_core", "code_builder", "collection", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http_multi_server", "io", "js", "logging", "meta", "mime", "package_config", "path", "pool", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web_socket_channel", "yaml"]}, {"name": "riverpod_generator", "version": "2.4.0", "dependencies": ["analyzer", "build", "build_config", "collection", "crypto", "meta", "path", "riverpod_analyzer_utils", "riverpod_annotation", "source_gen"]}, {"name": "flutter_lints", "version": "6.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["clock", "collection", "fake_async", "flutter", "leak_tracker_flutter_testing", "matcher", "meta", "path", "stack_trace", "stream_channel", "test_api", "vector_math"]}, {"name": "google_fonts", "version": "6.3.1", "dependencies": ["crypto", "flutter", "http", "path_provider"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "mobile_scanner", "version": "7.0.1", "dependencies": ["collection", "flutter", "flutter_web_plugins", "meta", "plugin_platform_interface", "web"]}, {"name": "phone_numbers_parser", "version": "9.0.11", "dependencies": ["meta"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "http", "version": "1.5.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "file_picker", "version": "10.3.2", "dependencies": ["cross_file", "dbus", "ffi", "flutter", "flutter_plugin_android_lifecycle", "flutter_web_plugins", "path", "plugin_platform_interface", "web", "win32"]}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "table_calendar", "version": "3.2.0", "dependencies": ["flutter", "intl", "simple_gesture_detector"]}, {"name": "url_launcher", "version": "6.3.2", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "geolocator", "version": "14.0.2", "dependencies": ["flutter", "geolocator_android", "geolocator_apple", "geolocator_linux", "geolocator_platform_interface", "geolocator_web", "geolocator_windows"]}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter_secure_storage", "version": "9.2.4", "dependencies": ["flutter", "flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_platform_interface", "flutter_secure_storage_web", "flutter_secure_storage_windows", "meta"]}, {"name": "freezed_annotation", "version": "2.4.4", "dependencies": ["collection", "json_annotation", "meta"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "hive_flutter", "version": "1.1.0", "dependencies": ["flutter", "hive", "path", "path_provider"]}, {"name": "hive", "version": "2.2.3", "dependencies": ["crypto", "meta"]}, {"name": "go_router", "version": "16.2.1", "dependencies": ["collection", "flutter", "flutter_web_plugins", "logging", "meta"]}, {"name": "dio", "version": "5.9.0", "dependencies": ["async", "collection", "dio_web_adapter", "http_parser", "meta", "mime", "path"]}, {"name": "riverpod_annotation", "version": "2.6.1", "dependencies": ["meta", "riverpod"]}, {"name": "flutter_riverpod", "version": "2.6.1", "dependencies": ["collection", "flutter", "meta", "riverpod", "state_notifier"]}, {"name": "riverpod", "version": "2.6.1", "dependencies": ["collection", "meta", "stack_trace", "state_notifier"]}, {"name": "flutter_local_notifications", "version": "17.2.4", "dependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "timezone"]}, {"name": "firebase_messaging", "version": "15.2.10", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_messaging_platform_interface", "firebase_messaging_web", "flutter", "meta"]}, {"name": "firebase_core", "version": "3.15.2", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "flutter_localizations", "version": "0.0.0", "dependencies": ["flutter", "intl", "path"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "cli_util", "version": "0.4.2", "dependencies": ["meta", "path"]}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "riverpod_analyzer_utils", "version": "0.5.1", "dependencies": ["analyzer", "collection", "crypto", "custom_lint_core", "freezed_annotation", "meta", "path", "source_span"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "custom_lint_builder", "version": "0.6.4", "dependencies": ["analyzer", "analyzer_plugin", "collection", "custom_lint", "custom_lint_core", "glob", "<PERSON><PERSON><PERSON><PERSON>", "meta", "package_config", "path", "pubspec_parse", "rxdart"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "analyzer_plugin", "version": "0.11.3", "dependencies": ["analyzer", "collection", "dart_style", "pub_semver", "yaml"]}, {"name": "analyzer", "version": "6.4.1", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "rxdart", "version": "0.27.7", "dependencies": []}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "custom_lint_core", "version": "0.6.3", "dependencies": ["analyzer", "analyzer_plugin", "collection", "glob", "matcher", "meta", "package_config", "path", "pubspec_parse", "source_span", "yaml"]}, {"name": "ci", "version": "0.1.0", "dependencies": []}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "test_api", "version": "0.7.6", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "source_gen", "version": "1.5.0", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "source_span", "yaml"]}, {"name": "build_config", "version": "1.1.2", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse", "yaml"]}, {"name": "build", "version": "2.4.1", "dependencies": ["analyzer", "async", "convert", "crypto", "glob", "logging", "meta", "package_config", "path"]}, {"name": "source_helper", "version": "1.3.5", "dependencies": ["analyzer", "collection", "source_gen"]}, {"name": "watcher", "version": "1.1.3", "dependencies": ["async", "path"]}, {"name": "timing", "version": "1.0.2", "dependencies": ["json_annotation"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "shelf_web_socket", "version": "2.0.1", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "dart_style", "version": "2.3.6", "dependencies": ["analyzer", "args", "collection", "path", "pub_semver", "source_span"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "build_runner_core", "version": "7.3.2", "dependencies": ["async", "build", "build_config", "build_resolvers", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "build_resolvers", "version": "2.4.2", "dependencies": ["analyzer", "async", "build", "collection", "convert", "crypto", "graphs", "logging", "package_config", "path", "pool", "pub_semver", "stream_transform", "yaml"]}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "lints", "version": "6.0.0", "dependencies": []}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.10", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.2.0", "dependencies": []}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["flutter"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.30", "dependencies": ["flutter"]}, {"name": "simple_gesture_detector", "version": "0.2.1", "dependencies": ["flutter"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_macos", "version": "3.2.3", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.3.18", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "geolocator_linux", "version": "0.2.3", "dependencies": ["dbus", "flutter", "geoclue", "geolocator_platform_interface", "gsettings", "package_info_plus"]}, {"name": "geolocator_windows", "version": "0.2.5", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_web", "version": "4.1.3", "dependencies": ["flutter", "flutter_web_plugins", "geolocator_platform_interface", "web"]}, {"name": "geolocator_apple", "version": "2.3.13", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_android", "version": "5.0.2", "dependencies": ["flutter", "geolocator_platform_interface", "meta", "uuid"]}, {"name": "geolocator_platform_interface", "version": "4.2.6", "dependencies": ["flutter", "meta", "plugin_platform_interface", "vector_math"]}, {"name": "flutter_secure_storage_windows", "version": "3.1.2", "dependencies": ["ffi", "flutter", "flutter_secure_storage_platform_interface", "path", "path_provider", "win32"]}, {"name": "flutter_secure_storage_web", "version": "1.2.1", "dependencies": ["flutter", "flutter_secure_storage_platform_interface", "flutter_web_plugins", "js"]}, {"name": "flutter_secure_storage_platform_interface", "version": "1.1.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_secure_storage_macos", "version": "3.1.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "flutter_secure_storage_linux", "version": "1.2.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.2", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.18", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.12", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "dio_web_adapter", "version": "2.1.1", "dependencies": ["dio", "http_parser", "meta", "web"]}, {"name": "state_notifier", "version": "1.0.0", "dependencies": ["meta"]}, {"name": "timezone", "version": "0.9.4", "dependencies": ["path"]}, {"name": "flutter_local_notifications_platform_interface", "version": "7.2.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_local_notifications_linux", "version": "4.0.1", "dependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"]}, {"name": "firebase_messaging_web", "version": "3.10.10", "dependencies": ["_flutterfire_internals", "firebase_core", "firebase_core_web", "firebase_messaging_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_messaging_platform_interface", "version": "4.6.10", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_platform_interface", "version": "6.0.0", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_web", "version": "2.24.1", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "xml", "version": "6.6.1", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "archive", "version": "4.0.7", "dependencies": ["crypto", "path", "posix"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "4.3.0", "dependencies": ["collection", "logging", "path", "stream_transform", "vm_service", "watcher"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "_fe_analyzer_shared", "version": "67.0.0", "dependencies": ["meta"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "built_value", "version": "8.11.1", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "leak_tracker_testing", "version": "3.0.2", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "11.0.1", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "package_info_plus", "version": "8.3.1", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "gsettings", "version": "0.2.8", "dependencies": ["dbus", "xdg_directories"]}, {"name": "geoclue", "version": "0.1.1", "dependencies": ["dbus", "meta"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "_flutterfire_internals", "version": "1.3.59", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "petitparser", "version": "7.0.1", "dependencies": ["collection", "meta"]}, {"name": "posix", "version": "6.0.3", "dependencies": ["ffi", "meta", "path"]}, {"name": "vm_service", "version": "15.0.2", "dependencies": []}, {"name": "package_info_plus_platform_interface", "version": "3.2.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}], "configVersion": 1}