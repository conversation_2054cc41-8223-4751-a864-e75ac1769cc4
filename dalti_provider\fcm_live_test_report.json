{"timestamp": "2025-09-06T14:23:39.795Z", "tests": {"configuration": "PASS", "tokenFormat": "PASS", "liveTest": "SKIPPED"}, "recommendations": ["⚠️  No Android device detected - connect device for testing", "🔧 To test notifications:", "   1. Connect Android device", "   2. Run: flutter run", "   3. Tap the \"🚀 Test FCM\" button in the app", "   4. Check console logs for token", "   5. Use Firebase Console to send test notification"]}