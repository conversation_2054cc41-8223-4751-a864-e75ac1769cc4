# PowerShell script to test FCM using Firebase CLI access token
# This script gets an access token from Firebase CLI and sends a test notification

Write-Host "🔥 FCM TESTING WITH FIREBASE CLI" -ForegroundColor Yellow
Write-Host "=================================" -ForegroundColor Yellow
Write-Host ""

# Configuration
$PROJECT_ID = "dalti-prod"
$FCM_TOKEN = "dX8_yF2uRyyVvcN-AmfjFl:APA91bFuCLThVntBVy-OHA_w12zbh1T7XYXcdV5nAKlxC1VRrFYfrMfHc5C3NjnhBIVW2ccv-QIBumqGpX5yMGQcgtuRNXp9RY3dJjK5mWGWKLYH9DtAuIM"

Write-Host "📋 Configuration:" -ForegroundColor Cyan
Write-Host "   Project ID: $PROJECT_ID" -ForegroundColor White
Write-Host "   FCM Token: $($FCM_TOKEN.Substring(0, 30))..." -ForegroundColor White
Write-Host ""

# Step 1: Get access token from Firebase CLI
Write-Host "1️⃣  Getting Firebase access token..." -ForegroundColor Green

try {
    # Try to get access token using Firebase CLI
    $accessTokenOutput = npx -y firebase-tools@latest auth:print-access-token 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        $accessToken = $accessTokenOutput.Trim()
        Write-Host "✅ Access token obtained: $($accessToken.Substring(0, 20))..." -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to get access token from Firebase CLI" -ForegroundColor Red
        Write-Host "Error: $accessTokenOutput" -ForegroundColor Red
        Write-Host ""
        Write-Host "🔧 Try running: npx -y firebase-tools@latest login" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "❌ Error getting access token: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Step 2: Prepare FCM payload
Write-Host "2️⃣  Preparing FCM notification payload..." -ForegroundColor Green

$payload = @{
    message = @{
        token = $FCM_TOKEN
        notification = @{
            title = "🚀 Firebase CLI Test"
            body = "Testing FCM delivery via Firebase CLI access token"
        }
        data = @{
            test_type = "firebase_cli"
            timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            source = "powershell_script"
        }
        android = @{
            priority = "high"
            notification = @{
                channel_id = "dalti_provider_notifications"
                icon = "launcher_icon"
                color = "#15424E"
                sound = "default"
            }
        }
    }
} | ConvertTo-Json -Depth 5

Write-Host "✅ Payload prepared" -ForegroundColor Green
Write-Host "📦 Payload preview:" -ForegroundColor Cyan
Write-Host $payload -ForegroundColor White
Write-Host ""

# Step 3: Send FCM notification
Write-Host "3️⃣  Sending FCM notification..." -ForegroundColor Green

$uri = "https://fcm.googleapis.com/v1/projects/$PROJECT_ID/messages:send"
$headers = @{
    "Authorization" = "Bearer $accessToken"
    "Content-Type" = "application/json"
}

Write-Host "🔗 Endpoint: $uri" -ForegroundColor Cyan
Write-Host "🔑 Authorization: Bearer $($accessToken.Substring(0, 20))..." -ForegroundColor Cyan
Write-Host ""

try {
    Write-Host "📤 Sending request..." -ForegroundColor Yellow
    
    $response = Invoke-RestMethod -Uri $uri -Method POST -Headers $headers -Body $payload -ErrorAction Stop
    
    Write-Host "🎉 SUCCESS! Notification sent successfully!" -ForegroundColor Green
    Write-Host "📱 Check your Android device now - you should see the notification!" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "📋 Response details:" -ForegroundColor Cyan
    Write-Host ($response | ConvertTo-Json -Depth 3) -ForegroundColor White
    
} catch {
    Write-Host "❌ FAILED to send notification" -ForegroundColor Red
    Write-Host ""
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "📊 HTTP Status: $statusCode" -ForegroundColor Red
        
        try {
            $errorStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorStream)
            $errorBody = $reader.ReadToEnd()
            $reader.Close()
            
            Write-Host "📥 Error Response:" -ForegroundColor Red
            Write-Host $errorBody -ForegroundColor White
            
            # Try to parse as JSON for better formatting
            try {
                $errorJson = $errorBody | ConvertFrom-Json
                Write-Host ""
                Write-Host "🔍 Parsed Error:" -ForegroundColor Red
                Write-Host "   Code: $($errorJson.error.code)" -ForegroundColor White
                Write-Host "   Message: $($errorJson.error.message)" -ForegroundColor White
                Write-Host "   Status: $($errorJson.error.status)" -ForegroundColor White
                
                # Provide specific guidance based on error
                switch ($errorJson.error.code) {
                    401 { 
                        Write-Host ""
                        Write-Host "💡 SOLUTION: Authentication issue" -ForegroundColor Yellow
                        Write-Host "   Try: npx -y firebase-tools@latest login" -ForegroundColor Yellow
                    }
                    403 { 
                        Write-Host ""
                        Write-Host "💡 SOLUTION: Permission issue" -ForegroundColor Yellow
                        Write-Host "   Check if you have FCM permissions for project: $PROJECT_ID" -ForegroundColor Yellow
                    }
                    404 { 
                        Write-Host ""
                        Write-Host "💡 SOLUTION: Project not found" -ForegroundColor Yellow
                        Write-Host "   Verify project ID: $PROJECT_ID" -ForegroundColor Yellow
                    }
                    400 { 
                        Write-Host ""
                        Write-Host "💡 SOLUTION: Invalid request" -ForegroundColor Yellow
                        Write-Host "   Check FCM token format and payload structure" -ForegroundColor Yellow
                    }
                }
            } catch {
                # Error body is not JSON
            }
            
        } catch {
            Write-Host "Could not read error response body" -ForegroundColor Red
        }
    } else {
        Write-Host "📥 Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "📊 DEBUGGING SUMMARY" -ForegroundColor Yellow
Write-Host "====================" -ForegroundColor Yellow
Write-Host ""

if ($response) {
    Write-Host "✅ FCM API is working correctly" -ForegroundColor Green
    Write-Host "✅ Authentication is successful" -ForegroundColor Green
    Write-Host "✅ Project configuration is correct" -ForegroundColor Green
    Write-Host ""
    Write-Host "🔍 If notification did not appear on device:" -ForegroundColor Yellow
    Write-Host "   1. Check device notification settings" -ForegroundColor White
    Write-Host "   2. Verify app notification permissions" -ForegroundColor White
    Write-Host "   3. Check Do Not Disturb mode" -ForegroundColor White
    Write-Host "   4. Verify battery optimization settings" -ForegroundColor White
    Write-Host "   5. Try with app in foreground vs background" -ForegroundColor White
} else {
    Write-Host "❌ FCM API request failed" -ForegroundColor Red
    Write-Host "🔧 Fix the authentication/configuration issues above" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎯 NEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. If the API call succeeded, the issue is device-specific" -ForegroundColor White
Write-Host "2. If the API call failed, the issue is project/authentication related" -ForegroundColor White
Write-Host "3. Check the Flutter console logs for any FCM message reception" -ForegroundColor White

Write-Host ""
Write-Host "📱 DEVICE TESTING:" -ForegroundColor Cyan
Write-Host "- Keep the Dalti Provider app open while testing" -ForegroundColor White
Write-Host "- Watch both the notification panel AND the Flutter console logs" -ForegroundColor White
Write-Host "- Try testing with app in foreground, then background" -ForegroundColor White
