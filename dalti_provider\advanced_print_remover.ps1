# Advanced PowerShell script to remove print statements from Dart files
# Handles complex cases including multi-line prints, nested parentheses, etc.

param(
    [string]$LibPath = "lib",
    [switch]$DryRun = $false,
    [switch]$Backup = $true
)

function Remove-PrintStatements {
    param([string]$Content)
    
    # Split content into lines for line-by-line processing
    $lines = $Content -split "`n"
    $result = @()
    $i = 0
    
    while ($i -lt $lines.Length) {
        $line = $lines[$i]
        
        # Check if line contains print statement
        if ($line -match "^\s*print\s*\(") {
            # Handle multi-line print statements
            $printBlock = $line
            $parenCount = ($line.ToCharArray() | Where-Object { $_ -eq '(' }).Count - ($line.ToCharArray() | Where-Object { $_ -eq ')' }).Count
            
            # If parentheses are not balanced, continue to next lines
            while ($parenCount -gt 0 -and $i + 1 -lt $lines.Length) {
                $i++
                $nextLine = $lines[$i]
                $printBlock += "`n" + $nextLine
                $parenCount += ($nextLine.ToCharArray() | Where-Object { $_ -eq '(' }).Count - ($nextLine.ToCharArray() | Where-Object { $_ -eq ')' }).Count
            }
            
            # Skip this print block (don't add to result)
            Write-Verbose "Removed print block: $($printBlock.Substring(0, [Math]::Min(50, $printBlock.Length)))"
        }
        else {
            # Keep non-print lines
            $result += $line
        }
        $i++
    }
    
    # Join lines back and clean up extra empty lines
    $finalContent = $result -join "`n"
    $finalContent = $finalContent -replace "(?m)^\s*\n\s*\n\s*\n", "`n`n"
    
    return $finalContent
}

Write-Host "🚀 Advanced Print Statement Remover" -ForegroundColor Green
Write-Host "📁 Target: $LibPath" -ForegroundColor Cyan
Write-Host "🔄 Backup: $Backup" -ForegroundColor Cyan
Write-Host "🧪 Dry Run: $DryRun" -ForegroundColor Yellow

$dartFiles = Get-ChildItem -Path $LibPath -Recurse -Filter "*.dart"
$stats = @{
    FilesProcessed = 0
    PrintStatementsRemoved = 0
    FilesWithPrints = 0
}

foreach ($file in $dartFiles) {
    $originalContent = Get-Content $file.FullName -Raw
    $printCount = ([regex]::Matches($originalContent, "print\s*\(")).Count
    
    if ($printCount -gt 0) {
        Write-Host "🔧 $($file.Name): $printCount print statements" -ForegroundColor Yellow
        
        if ($Backup -and -not $DryRun) {
            Copy-Item $file.FullName "$($file.FullName).backup"
        }
        
        $newContent = Remove-PrintStatements -Content $originalContent
        
        if (-not $DryRun) {
            Set-Content -Path $file.FullName -Value $newContent -NoNewline
        }
        
        $stats.FilesWithPrints++
        $stats.PrintStatementsRemoved += $printCount
    }
    
    $stats.FilesProcessed++
}

Write-Host "`n✅ COMPLETED!" -ForegroundColor Green
Write-Host "📊 Files processed: $($stats.FilesProcessed)" -ForegroundColor Cyan
Write-Host "📄 Files with prints: $($stats.FilesWithPrints)" -ForegroundColor Cyan
Write-Host "🗑️ Print statements removed: $($stats.PrintStatementsRemoved)" -ForegroundColor Cyan

if ($DryRun) {
    Write-Host "`n⚠️ DRY RUN - No changes made" -ForegroundColor Yellow
}
