// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'appointment_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$appointmentApiServiceHash() =>
    r'eaa87c30f3a3f1fe7672b8152240b103772cf6fb';

/// Provider for AppointmentApiService
///
/// Copied from [appointmentApiService].
@ProviderFor(appointmentApiService)
final appointmentApiServiceProvider =
    AutoDisposeProvider<AppointmentApiService>.internal(
  appointmentApiService,
  name: r'appointmentApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$appointmentApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AppointmentApiServiceRef
    = AutoDisposeProviderRef<AppointmentApiService>;
String _$appointmentRepositoryHash() =>
    r'8f14cece2e4853e549717a43392482dd08829a58';

/// Provider for AppointmentRepository
///
/// Copied from [appointmentRepository].
@ProviderFor(appointmentRepository)
final appointmentRepositoryProvider =
    AutoDisposeProvider<AppointmentRepository>.internal(
  appointmentRepository,
  name: r'appointmentRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$appointmentRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AppointmentRepositoryRef
    = AutoDisposeProviderRef<AppointmentRepository>;
String _$appointmentNotifierHash() =>
    r'088af6cc40fb69aecdfa53cac7174e1ede96e1a3';

/// Provider for appointments list with filters
///
/// Copied from [AppointmentNotifier].
@ProviderFor(AppointmentNotifier)
final appointmentNotifierProvider = AutoDisposeNotifierProvider<
    AppointmentNotifier, AsyncValue<List<Appointment>>>.internal(
  AppointmentNotifier.new,
  name: r'appointmentNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$appointmentNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AppointmentNotifier
    = AutoDisposeNotifier<AsyncValue<List<Appointment>>>;
String _$singleAppointmentNotifierHash() =>
    r'd5d9922e8d70b45a5dfad7f90113475e0268ec48';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$SingleAppointmentNotifier
    extends BuildlessAutoDisposeNotifier<AsyncValue<Appointment?>> {
  late final String appointmentId;

  AsyncValue<Appointment?> build(
    String appointmentId,
  );
}

/// Provider for single appointment
///
/// Copied from [SingleAppointmentNotifier].
@ProviderFor(SingleAppointmentNotifier)
const singleAppointmentNotifierProvider = SingleAppointmentNotifierFamily();

/// Provider for single appointment
///
/// Copied from [SingleAppointmentNotifier].
class SingleAppointmentNotifierFamily extends Family<AsyncValue<Appointment?>> {
  /// Provider for single appointment
  ///
  /// Copied from [SingleAppointmentNotifier].
  const SingleAppointmentNotifierFamily();

  /// Provider for single appointment
  ///
  /// Copied from [SingleAppointmentNotifier].
  SingleAppointmentNotifierProvider call(
    String appointmentId,
  ) {
    return SingleAppointmentNotifierProvider(
      appointmentId,
    );
  }

  @override
  SingleAppointmentNotifierProvider getProviderOverride(
    covariant SingleAppointmentNotifierProvider provider,
  ) {
    return call(
      provider.appointmentId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'singleAppointmentNotifierProvider';
}

/// Provider for single appointment
///
/// Copied from [SingleAppointmentNotifier].
class SingleAppointmentNotifierProvider extends AutoDisposeNotifierProviderImpl<
    SingleAppointmentNotifier, AsyncValue<Appointment?>> {
  /// Provider for single appointment
  ///
  /// Copied from [SingleAppointmentNotifier].
  SingleAppointmentNotifierProvider(
    String appointmentId,
  ) : this._internal(
          () => SingleAppointmentNotifier()..appointmentId = appointmentId,
          from: singleAppointmentNotifierProvider,
          name: r'singleAppointmentNotifierProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$singleAppointmentNotifierHash,
          dependencies: SingleAppointmentNotifierFamily._dependencies,
          allTransitiveDependencies:
              SingleAppointmentNotifierFamily._allTransitiveDependencies,
          appointmentId: appointmentId,
        );

  SingleAppointmentNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.appointmentId,
  }) : super.internal();

  final String appointmentId;

  @override
  AsyncValue<Appointment?> runNotifierBuild(
    covariant SingleAppointmentNotifier notifier,
  ) {
    return notifier.build(
      appointmentId,
    );
  }

  @override
  Override overrideWith(SingleAppointmentNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: SingleAppointmentNotifierProvider._internal(
        () => create()..appointmentId = appointmentId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        appointmentId: appointmentId,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<SingleAppointmentNotifier,
      AsyncValue<Appointment?>> createElement() {
    return _SingleAppointmentNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SingleAppointmentNotifierProvider &&
        other.appointmentId == appointmentId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, appointmentId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SingleAppointmentNotifierRef
    on AutoDisposeNotifierProviderRef<AsyncValue<Appointment?>> {
  /// The parameter `appointmentId` of this provider.
  String get appointmentId;
}

class _SingleAppointmentNotifierProviderElement
    extends AutoDisposeNotifierProviderElement<SingleAppointmentNotifier,
        AsyncValue<Appointment?>> with SingleAppointmentNotifierRef {
  _SingleAppointmentNotifierProviderElement(super.provider);

  @override
  String get appointmentId =>
      (origin as SingleAppointmentNotifierProvider).appointmentId;
}

String _$todaysAppointmentsNotifierHash() =>
    r'aa7b3c6517d5b9d1f9446713a86af43f85ad12b8';

/// Provider for today's appointments
///
/// Copied from [TodaysAppointmentsNotifier].
@ProviderFor(TodaysAppointmentsNotifier)
final todaysAppointmentsNotifierProvider = AutoDisposeNotifierProvider<
    TodaysAppointmentsNotifier, AsyncValue<List<Appointment>>>.internal(
  TodaysAppointmentsNotifier.new,
  name: r'todaysAppointmentsNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$todaysAppointmentsNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TodaysAppointmentsNotifier
    = AutoDisposeNotifier<AsyncValue<List<Appointment>>>;
String _$upcomingAppointmentsNotifierHash() =>
    r'69c27ac2d6a2319a2494b4775ed042a51608eab5';

/// Provider for upcoming appointments
///
/// Copied from [UpcomingAppointmentsNotifier].
@ProviderFor(UpcomingAppointmentsNotifier)
final upcomingAppointmentsNotifierProvider = AutoDisposeNotifierProvider<
    UpcomingAppointmentsNotifier, AsyncValue<List<Appointment>>>.internal(
  UpcomingAppointmentsNotifier.new,
  name: r'upcomingAppointmentsNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$upcomingAppointmentsNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpcomingAppointmentsNotifier
    = AutoDisposeNotifier<AsyncValue<List<Appointment>>>;
String _$appointmentStatsNotifierHash() =>
    r'7e396ef8415dcfcf8b911395a64c6e214641c779';

abstract class _$AppointmentStatsNotifier
    extends BuildlessAutoDisposeNotifier<AsyncValue<AppointmentStats>> {
  late final DateTime startDate;
  late final DateTime endDate;
  late final String? locationId;

  AsyncValue<AppointmentStats> build({
    required DateTime startDate,
    required DateTime endDate,
    String? locationId,
  });
}

/// Provider for appointment statistics
///
/// Copied from [AppointmentStatsNotifier].
@ProviderFor(AppointmentStatsNotifier)
const appointmentStatsNotifierProvider = AppointmentStatsNotifierFamily();

/// Provider for appointment statistics
///
/// Copied from [AppointmentStatsNotifier].
class AppointmentStatsNotifierFamily
    extends Family<AsyncValue<AppointmentStats>> {
  /// Provider for appointment statistics
  ///
  /// Copied from [AppointmentStatsNotifier].
  const AppointmentStatsNotifierFamily();

  /// Provider for appointment statistics
  ///
  /// Copied from [AppointmentStatsNotifier].
  AppointmentStatsNotifierProvider call({
    required DateTime startDate,
    required DateTime endDate,
    String? locationId,
  }) {
    return AppointmentStatsNotifierProvider(
      startDate: startDate,
      endDate: endDate,
      locationId: locationId,
    );
  }

  @override
  AppointmentStatsNotifierProvider getProviderOverride(
    covariant AppointmentStatsNotifierProvider provider,
  ) {
    return call(
      startDate: provider.startDate,
      endDate: provider.endDate,
      locationId: provider.locationId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'appointmentStatsNotifierProvider';
}

/// Provider for appointment statistics
///
/// Copied from [AppointmentStatsNotifier].
class AppointmentStatsNotifierProvider extends AutoDisposeNotifierProviderImpl<
    AppointmentStatsNotifier, AsyncValue<AppointmentStats>> {
  /// Provider for appointment statistics
  ///
  /// Copied from [AppointmentStatsNotifier].
  AppointmentStatsNotifierProvider({
    required DateTime startDate,
    required DateTime endDate,
    String? locationId,
  }) : this._internal(
          () => AppointmentStatsNotifier()
            ..startDate = startDate
            ..endDate = endDate
            ..locationId = locationId,
          from: appointmentStatsNotifierProvider,
          name: r'appointmentStatsNotifierProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$appointmentStatsNotifierHash,
          dependencies: AppointmentStatsNotifierFamily._dependencies,
          allTransitiveDependencies:
              AppointmentStatsNotifierFamily._allTransitiveDependencies,
          startDate: startDate,
          endDate: endDate,
          locationId: locationId,
        );

  AppointmentStatsNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.startDate,
    required this.endDate,
    required this.locationId,
  }) : super.internal();

  final DateTime startDate;
  final DateTime endDate;
  final String? locationId;

  @override
  AsyncValue<AppointmentStats> runNotifierBuild(
    covariant AppointmentStatsNotifier notifier,
  ) {
    return notifier.build(
      startDate: startDate,
      endDate: endDate,
      locationId: locationId,
    );
  }

  @override
  Override overrideWith(AppointmentStatsNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: AppointmentStatsNotifierProvider._internal(
        () => create()
          ..startDate = startDate
          ..endDate = endDate
          ..locationId = locationId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        startDate: startDate,
        endDate: endDate,
        locationId: locationId,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<AppointmentStatsNotifier,
      AsyncValue<AppointmentStats>> createElement() {
    return _AppointmentStatsNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AppointmentStatsNotifierProvider &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.locationId == locationId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, startDate.hashCode);
    hash = _SystemHash.combine(hash, endDate.hashCode);
    hash = _SystemHash.combine(hash, locationId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin AppointmentStatsNotifierRef
    on AutoDisposeNotifierProviderRef<AsyncValue<AppointmentStats>> {
  /// The parameter `startDate` of this provider.
  DateTime get startDate;

  /// The parameter `endDate` of this provider.
  DateTime get endDate;

  /// The parameter `locationId` of this provider.
  String? get locationId;
}

class _AppointmentStatsNotifierProviderElement
    extends AutoDisposeNotifierProviderElement<AppointmentStatsNotifier,
        AsyncValue<AppointmentStats>> with AppointmentStatsNotifierRef {
  _AppointmentStatsNotifierProviderElement(super.provider);

  @override
  DateTime get startDate =>
      (origin as AppointmentStatsNotifierProvider).startDate;
  @override
  DateTime get endDate => (origin as AppointmentStatsNotifierProvider).endDate;
  @override
  String? get locationId =>
      (origin as AppointmentStatsNotifierProvider).locationId;
}

String _$timeSlotNotifierHash() => r'0b056a269026bfab3424b0138015a5c0c4c81c9a';

abstract class _$TimeSlotNotifier
    extends BuildlessAutoDisposeNotifier<AsyncValue<List<TimeSlot>>> {
  late final DateTime date;
  late final String serviceId;
  late final String? locationId;
  late final String? queueId;

  AsyncValue<List<TimeSlot>> build({
    required DateTime date,
    required String serviceId,
    String? locationId,
    String? queueId,
  });
}

/// Provider for available time slots
///
/// Copied from [TimeSlotNotifier].
@ProviderFor(TimeSlotNotifier)
const timeSlotNotifierProvider = TimeSlotNotifierFamily();

/// Provider for available time slots
///
/// Copied from [TimeSlotNotifier].
class TimeSlotNotifierFamily extends Family<AsyncValue<List<TimeSlot>>> {
  /// Provider for available time slots
  ///
  /// Copied from [TimeSlotNotifier].
  const TimeSlotNotifierFamily();

  /// Provider for available time slots
  ///
  /// Copied from [TimeSlotNotifier].
  TimeSlotNotifierProvider call({
    required DateTime date,
    required String serviceId,
    String? locationId,
    String? queueId,
  }) {
    return TimeSlotNotifierProvider(
      date: date,
      serviceId: serviceId,
      locationId: locationId,
      queueId: queueId,
    );
  }

  @override
  TimeSlotNotifierProvider getProviderOverride(
    covariant TimeSlotNotifierProvider provider,
  ) {
    return call(
      date: provider.date,
      serviceId: provider.serviceId,
      locationId: provider.locationId,
      queueId: provider.queueId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'timeSlotNotifierProvider';
}

/// Provider for available time slots
///
/// Copied from [TimeSlotNotifier].
class TimeSlotNotifierProvider extends AutoDisposeNotifierProviderImpl<
    TimeSlotNotifier, AsyncValue<List<TimeSlot>>> {
  /// Provider for available time slots
  ///
  /// Copied from [TimeSlotNotifier].
  TimeSlotNotifierProvider({
    required DateTime date,
    required String serviceId,
    String? locationId,
    String? queueId,
  }) : this._internal(
          () => TimeSlotNotifier()
            ..date = date
            ..serviceId = serviceId
            ..locationId = locationId
            ..queueId = queueId,
          from: timeSlotNotifierProvider,
          name: r'timeSlotNotifierProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$timeSlotNotifierHash,
          dependencies: TimeSlotNotifierFamily._dependencies,
          allTransitiveDependencies:
              TimeSlotNotifierFamily._allTransitiveDependencies,
          date: date,
          serviceId: serviceId,
          locationId: locationId,
          queueId: queueId,
        );

  TimeSlotNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.date,
    required this.serviceId,
    required this.locationId,
    required this.queueId,
  }) : super.internal();

  final DateTime date;
  final String serviceId;
  final String? locationId;
  final String? queueId;

  @override
  AsyncValue<List<TimeSlot>> runNotifierBuild(
    covariant TimeSlotNotifier notifier,
  ) {
    return notifier.build(
      date: date,
      serviceId: serviceId,
      locationId: locationId,
      queueId: queueId,
    );
  }

  @override
  Override overrideWith(TimeSlotNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: TimeSlotNotifierProvider._internal(
        () => create()
          ..date = date
          ..serviceId = serviceId
          ..locationId = locationId
          ..queueId = queueId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        date: date,
        serviceId: serviceId,
        locationId: locationId,
        queueId: queueId,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<TimeSlotNotifier,
      AsyncValue<List<TimeSlot>>> createElement() {
    return _TimeSlotNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TimeSlotNotifierProvider &&
        other.date == date &&
        other.serviceId == serviceId &&
        other.locationId == locationId &&
        other.queueId == queueId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, date.hashCode);
    hash = _SystemHash.combine(hash, serviceId.hashCode);
    hash = _SystemHash.combine(hash, locationId.hashCode);
    hash = _SystemHash.combine(hash, queueId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin TimeSlotNotifierRef
    on AutoDisposeNotifierProviderRef<AsyncValue<List<TimeSlot>>> {
  /// The parameter `date` of this provider.
  DateTime get date;

  /// The parameter `serviceId` of this provider.
  String get serviceId;

  /// The parameter `locationId` of this provider.
  String? get locationId;

  /// The parameter `queueId` of this provider.
  String? get queueId;
}

class _TimeSlotNotifierProviderElement
    extends AutoDisposeNotifierProviderElement<TimeSlotNotifier,
        AsyncValue<List<TimeSlot>>> with TimeSlotNotifierRef {
  _TimeSlotNotifierProviderElement(super.provider);

  @override
  DateTime get date => (origin as TimeSlotNotifierProvider).date;
  @override
  String get serviceId => (origin as TimeSlotNotifierProvider).serviceId;
  @override
  String? get locationId => (origin as TimeSlotNotifierProvider).locationId;
  @override
  String? get queueId => (origin as TimeSlotNotifierProvider).queueId;
}

String _$calendarViewNotifierHash() =>
    r'18db1c640c610b37fc7bd9fbb5a37c8fff69b0bf';

abstract class _$CalendarViewNotifier
    extends BuildlessAutoDisposeNotifier<AsyncValue<List<CalendarViewData>>> {
  late final DateTime startDate;
  late final DateTime endDate;

  AsyncValue<List<CalendarViewData>> build({
    required DateTime startDate,
    required DateTime endDate,
  });
}

/// Provider for calendar view data
///
/// Copied from [CalendarViewNotifier].
@ProviderFor(CalendarViewNotifier)
const calendarViewNotifierProvider = CalendarViewNotifierFamily();

/// Provider for calendar view data
///
/// Copied from [CalendarViewNotifier].
class CalendarViewNotifierFamily
    extends Family<AsyncValue<List<CalendarViewData>>> {
  /// Provider for calendar view data
  ///
  /// Copied from [CalendarViewNotifier].
  const CalendarViewNotifierFamily();

  /// Provider for calendar view data
  ///
  /// Copied from [CalendarViewNotifier].
  CalendarViewNotifierProvider call({
    required DateTime startDate,
    required DateTime endDate,
  }) {
    return CalendarViewNotifierProvider(
      startDate: startDate,
      endDate: endDate,
    );
  }

  @override
  CalendarViewNotifierProvider getProviderOverride(
    covariant CalendarViewNotifierProvider provider,
  ) {
    return call(
      startDate: provider.startDate,
      endDate: provider.endDate,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'calendarViewNotifierProvider';
}

/// Provider for calendar view data
///
/// Copied from [CalendarViewNotifier].
class CalendarViewNotifierProvider extends AutoDisposeNotifierProviderImpl<
    CalendarViewNotifier, AsyncValue<List<CalendarViewData>>> {
  /// Provider for calendar view data
  ///
  /// Copied from [CalendarViewNotifier].
  CalendarViewNotifierProvider({
    required DateTime startDate,
    required DateTime endDate,
  }) : this._internal(
          () => CalendarViewNotifier()
            ..startDate = startDate
            ..endDate = endDate,
          from: calendarViewNotifierProvider,
          name: r'calendarViewNotifierProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$calendarViewNotifierHash,
          dependencies: CalendarViewNotifierFamily._dependencies,
          allTransitiveDependencies:
              CalendarViewNotifierFamily._allTransitiveDependencies,
          startDate: startDate,
          endDate: endDate,
        );

  CalendarViewNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.startDate,
    required this.endDate,
  }) : super.internal();

  final DateTime startDate;
  final DateTime endDate;

  @override
  AsyncValue<List<CalendarViewData>> runNotifierBuild(
    covariant CalendarViewNotifier notifier,
  ) {
    return notifier.build(
      startDate: startDate,
      endDate: endDate,
    );
  }

  @override
  Override overrideWith(CalendarViewNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: CalendarViewNotifierProvider._internal(
        () => create()
          ..startDate = startDate
          ..endDate = endDate,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        startDate: startDate,
        endDate: endDate,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<CalendarViewNotifier,
      AsyncValue<List<CalendarViewData>>> createElement() {
    return _CalendarViewNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CalendarViewNotifierProvider &&
        other.startDate == startDate &&
        other.endDate == endDate;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, startDate.hashCode);
    hash = _SystemHash.combine(hash, endDate.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin CalendarViewNotifierRef
    on AutoDisposeNotifierProviderRef<AsyncValue<List<CalendarViewData>>> {
  /// The parameter `startDate` of this provider.
  DateTime get startDate;

  /// The parameter `endDate` of this provider.
  DateTime get endDate;
}

class _CalendarViewNotifierProviderElement
    extends AutoDisposeNotifierProviderElement<CalendarViewNotifier,
        AsyncValue<List<CalendarViewData>>> with CalendarViewNotifierRef {
  _CalendarViewNotifierProviderElement(super.provider);

  @override
  DateTime get startDate => (origin as CalendarViewNotifierProvider).startDate;
  @override
  DateTime get endDate => (origin as CalendarViewNotifierProvider).endDate;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
