{"timestamp": "2025-09-06T16:11:45.352Z", "summary": {"passed": 10, "failed": 1, "warnings": 0}, "projectId": "dalti-prod", "token": "dX8_yF2uRyyVvcN-AmfjFl:APA91bFuCLThVntBVy-OHA_w12zbh1T7XYXcdV5nAKlxC1VRrFYfrMfHc5C3NjnhBIVW2ccv-QIBumqGpX5yMGQcgtuRNXp9RY3dJjK5mWGWKLYH9DtAuIM", "results": [{"test": "Token Format", "status": "pass", "message": "Valid format: 22 + 119 characters", "details": null, "timestamp": "2025-09-06T16:11:44.929Z"}, {"test": "Token Structure", "status": "pass", "message": "Valid FCM token structure (APA91b prefix)", "details": null, "timestamp": "2025-09-06T16:11:44.930Z"}, {"test": "Token Timestamp", "status": "pass", "message": "Validated at: 2025-09-06T16:11:44.931Z", "details": null, "timestamp": "2025-09-06T16:11:44.931Z"}, {"test": "Firebase Config", "status": "pass", "message": "Project ID: dalti-prod", "details": null, "timestamp": "2025-09-06T16:11:44.932Z"}, {"test": "Firebase Config", "status": "pass", "message": "Project Number: 1060372851323", "details": null, "timestamp": "2025-09-06T16:11:44.932Z"}, {"test": "Firebase Config", "status": "pass", "message": "App ID: 1:1060372851323:android:c968a0882c726c190690de", "details": null, "timestamp": "2025-09-06T16:11:44.933Z"}, {"test": "Firebase Config", "status": "pass", "message": "Package: org.adscloud.dalti.provider", "details": null, "timestamp": "2025-09-06T16:11:44.933Z"}, {"test": "API Key", "status": "pass", "message": "API key matches configuration", "details": null, "timestamp": "2025-09-06T16:11:44.933Z"}, {"test": "Firebase Services", "status": "pass", "message": "Services configuration found", "details": null, "timestamp": "2025-09-06T16:11:44.934Z"}, {"test": "Network Connectivity", "status": "pass", "message": "FCM servers reachable (404)", "details": null, "timestamp": "2025-09-06T16:11:45.154Z"}, {"test": "Legacy FCM API", "status": "fail", "message": "Invalid JSON response", "details": {"statusCode": 404, "rawResponse": "<HTML>\n<HEAD>\n<TITLE>Not Found</TITLE>\n</HEAD>\n<BODY BGCOLOR=\"#FFFFFF\" TEXT=\"#000000\">\n<!-- GSE Default Error -->\n<H1>Not Found</H1>\n<H2>Error 404</H2>\n</BODY>\n</HTML>\n", "error": "Unexpected token '<', \"<HTML>\n<HE\"... is not valid JSON"}, "timestamp": "2025-09-06T16:11:45.348Z"}]}