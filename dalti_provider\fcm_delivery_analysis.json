{"timestamp": "2025-09-06T16:00:50.999Z", "token": "dX8_yF2uRyyVvcN-AmfjFl:APA91bFuCLThVntBVy-OHA_w12zbh1T7XYXcdV5nAKlxC1VRrFYfrMfHc5C3NjnhBIVW2ccv-QIBumqGpX5yMGQcgtuRNXp9RY3dJjK5mWGWKLYH9DtAuIM", "summary": {"errors": 1, "warnings": 1, "successes": 16}, "findings": [{"type": "success", "message": "Project ID: dalti-prod", "solution": null, "emoji": "✅"}, {"type": "success", "message": "Project Number: 1060372851323", "solution": null, "emoji": "✅"}, {"type": "success", "message": "App ID: 1:1060372851323:android:c968a0882c726c190690de", "solution": null, "emoji": "✅"}, {"type": "success", "message": "Package: org.adscloud.dalti.provider", "solution": null, "emoji": "✅"}, {"type": "success", "message": "API Keys configured: 1 keys", "solution": null, "emoji": "✅"}, {"type": "success", "message": "Current API Key: AIzaSyDDcsbxchQIzUvl...", "solution": null, "emoji": "✅"}, {"type": "warning", "message": "No OAuth clients found", "solution": "This might be normal for some configurations", "emoji": "⚠️"}, {"type": "error", "message": "Firebase Messaging service NOT configured", "solution": "Enable Cloud Messaging in Firebase Console", "emoji": "❌"}, {"type": "success", "message": "Manifest channel ID: dalti_provider_notifications", "solution": null, "emoji": "✅"}, {"type": "success", "message": "Service uses correct channel ID (3 occurrences)", "solution": null, "emoji": "✅"}, {"type": "success", "message": "Notification channel creation implemented", "solution": null, "emoji": "✅"}, {"type": "success", "message": "High importance notifications configured", "solution": null, "emoji": "✅"}, {"type": "success", "message": "Foreground message handler configured", "solution": null, "emoji": "✅"}, {"type": "success", "message": "Foreground handler shows local notifications", "solution": null, "emoji": "✅"}, {"type": "success", "message": "Background message handler registered in main.dart", "solution": null, "emoji": "✅"}, {"type": "success", "message": "Token format valid: 22 + 119 characters", "solution": null, "emoji": "✅"}, {"type": "success", "message": "Token structure appears valid (APA91b prefix)", "solution": null, "emoji": "✅"}, {"type": "success", "message": "Token tested at: 2025-09-06T16:00:50.993Z", "solution": null, "emoji": "✅"}]}