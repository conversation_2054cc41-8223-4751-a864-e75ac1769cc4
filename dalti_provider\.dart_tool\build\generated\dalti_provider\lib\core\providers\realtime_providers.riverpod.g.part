// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$webSocketServiceHash() => r'b81fdd4b874b6d664d421ee66b916ac54b8e927b';

/// WebSocket service provider (singleton)
///
/// Copied from [webSocketService].
@ProviderFor(webSocketService)
final webSocketServiceProvider = Provider<WebSocketService>.internal(
  webSocketService,
  name: r'webSocketServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$webSocketServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef WebSocketServiceRef = ProviderRef<WebSocketService>;
String _$initializedWebSocketServiceHash() =>
    r'73768bfdb10ae7876207151b2dc7aeb67a3c2e0e';

/// Initialized WebSocket service provider
///
/// Copied from [initializedWebSocketService].
@ProviderFor(initializedWebSocketService)
final initializedWebSocketServiceProvider =
    AutoDisposeFutureProvider<WebSocketService>.internal(
  initializedWebSocketService,
  name: r'initializedWebSocketServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$initializedWebSocketServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef InitializedWebSocketServiceRef
    = AutoDisposeFutureProviderRef<WebSocketService>;
String _$messageApiServiceHash() => r'ed13de65c0c05694889e318998a422c6857cf43a';

/// Message API service provider
///
/// Copied from [messageApiService].
@ProviderFor(messageApiService)
final messageApiServiceProvider =
    AutoDisposeProvider<MessageApiService>.internal(
  messageApiService,
  name: r'messageApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$messageApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef MessageApiServiceRef = AutoDisposeProviderRef<MessageApiService>;
String _$realtimeNotificationServiceHash() =>
    r'154f8edebf77059f7e6b2dd7b7459dd21a23df24';

/// Real-time notification service provider
///
/// Copied from [realtimeNotificationService].
@ProviderFor(realtimeNotificationService)
final realtimeNotificationServiceProvider =
    Provider<RealtimeNotificationService>.internal(
  realtimeNotificationService,
  name: r'realtimeNotificationServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$realtimeNotificationServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef RealtimeNotificationServiceRef
    = ProviderRef<RealtimeNotificationService>;
String _$webSocketConnectionStateHash() =>
    r'a95937d9ce1faab3b06e56464f2c1b808d5dffb7';

/// WebSocket connection state provider
///
/// Copied from [webSocketConnectionState].
@ProviderFor(webSocketConnectionState)
final webSocketConnectionStateProvider =
    AutoDisposeStreamProvider<bool>.internal(
  webSocketConnectionState,
  name: r'webSocketConnectionStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$webSocketConnectionStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef WebSocketConnectionStateRef = AutoDisposeStreamProviderRef<bool>;
String _$realtimeConversationsHash() =>
    r'cdbcacfa8c2415f07b38841025df9d1e010d92af';

/// Real-time conversations provider
///
/// Copied from [realtimeConversations].
@ProviderFor(realtimeConversations)
final realtimeConversationsProvider =
    AutoDisposeStreamProvider<List<Conversation>>.internal(
  realtimeConversations,
  name: r'realtimeConversationsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$realtimeConversationsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef RealtimeConversationsRef
    = AutoDisposeStreamProviderRef<List<Conversation>>;
String _$realtimeMessagesHash() => r'dfb38142d3735e0d2e504d0a377e492926e130eb';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Real-time messages provider for a specific conversation
///
/// Copied from [realtimeMessages].
@ProviderFor(realtimeMessages)
const realtimeMessagesProvider = RealtimeMessagesFamily();

/// Real-time messages provider for a specific conversation
///
/// Copied from [realtimeMessages].
class RealtimeMessagesFamily extends Family<AsyncValue<List<Message>>> {
  /// Real-time messages provider for a specific conversation
  ///
  /// Copied from [realtimeMessages].
  const RealtimeMessagesFamily();

  /// Real-time messages provider for a specific conversation
  ///
  /// Copied from [realtimeMessages].
  RealtimeMessagesProvider call(
    String conversationId,
  ) {
    return RealtimeMessagesProvider(
      conversationId,
    );
  }

  @override
  RealtimeMessagesProvider getProviderOverride(
    covariant RealtimeMessagesProvider provider,
  ) {
    return call(
      provider.conversationId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'realtimeMessagesProvider';
}

/// Real-time messages provider for a specific conversation
///
/// Copied from [realtimeMessages].
class RealtimeMessagesProvider
    extends AutoDisposeStreamProvider<List<Message>> {
  /// Real-time messages provider for a specific conversation
  ///
  /// Copied from [realtimeMessages].
  RealtimeMessagesProvider(
    String conversationId,
  ) : this._internal(
          (ref) => realtimeMessages(
            ref as RealtimeMessagesRef,
            conversationId,
          ),
          from: realtimeMessagesProvider,
          name: r'realtimeMessagesProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$realtimeMessagesHash,
          dependencies: RealtimeMessagesFamily._dependencies,
          allTransitiveDependencies:
              RealtimeMessagesFamily._allTransitiveDependencies,
          conversationId: conversationId,
        );

  RealtimeMessagesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.conversationId,
  }) : super.internal();

  final String conversationId;

  @override
  Override overrideWith(
    Stream<List<Message>> Function(RealtimeMessagesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: RealtimeMessagesProvider._internal(
        (ref) => create(ref as RealtimeMessagesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        conversationId: conversationId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<List<Message>> createElement() {
    return _RealtimeMessagesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is RealtimeMessagesProvider &&
        other.conversationId == conversationId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, conversationId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin RealtimeMessagesRef on AutoDisposeStreamProviderRef<List<Message>> {
  /// The parameter `conversationId` of this provider.
  String get conversationId;
}

class _RealtimeMessagesProviderElement
    extends AutoDisposeStreamProviderElement<List<Message>>
    with RealtimeMessagesRef {
  _RealtimeMessagesProviderElement(super.provider);

  @override
  String get conversationId =>
      (origin as RealtimeMessagesProvider).conversationId;
}

String _$realtimeUnreadMessagesCountHash() =>
    r'48323e20ed07d45f3fc51fee01075818324dc4bd';

/// Real-time unread messages count provider
///
/// Copied from [realtimeUnreadMessagesCount].
@ProviderFor(realtimeUnreadMessagesCount)
final realtimeUnreadMessagesCountProvider =
    AutoDisposeStreamProvider<int>.internal(
  realtimeUnreadMessagesCount,
  name: r'realtimeUnreadMessagesCountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$realtimeUnreadMessagesCountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef RealtimeUnreadMessagesCountRef = AutoDisposeStreamProviderRef<int>;
String _$realtimeAllQueuesHash() => r'32aaaba0def7285128f6ff9a7c8109e2bf1ef658';

/// Real-time queue status provider for all queues
///
/// Copied from [realtimeAllQueues].
@ProviderFor(realtimeAllQueues)
final realtimeAllQueuesProvider =
    AutoDisposeStreamProvider<Map<String, QueueStatus>>.internal(
  realtimeAllQueues,
  name: r'realtimeAllQueuesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$realtimeAllQueuesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef RealtimeAllQueuesRef
    = AutoDisposeStreamProviderRef<Map<String, QueueStatus>>;
String _$realtimeQueueStatusHash() =>
    r'f2739053e238df5455c23abf3582d5c07ac2fd1d';

/// Real-time queue status provider for a specific queue
///
/// Copied from [realtimeQueueStatus].
@ProviderFor(realtimeQueueStatus)
const realtimeQueueStatusProvider = RealtimeQueueStatusFamily();

/// Real-time queue status provider for a specific queue
///
/// Copied from [realtimeQueueStatus].
class RealtimeQueueStatusFamily extends Family<AsyncValue<QueueStatus>> {
  /// Real-time queue status provider for a specific queue
  ///
  /// Copied from [realtimeQueueStatus].
  const RealtimeQueueStatusFamily();

  /// Real-time queue status provider for a specific queue
  ///
  /// Copied from [realtimeQueueStatus].
  RealtimeQueueStatusProvider call(
    String queueId,
  ) {
    return RealtimeQueueStatusProvider(
      queueId,
    );
  }

  @override
  RealtimeQueueStatusProvider getProviderOverride(
    covariant RealtimeQueueStatusProvider provider,
  ) {
    return call(
      provider.queueId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'realtimeQueueStatusProvider';
}

/// Real-time queue status provider for a specific queue
///
/// Copied from [realtimeQueueStatus].
class RealtimeQueueStatusProvider
    extends AutoDisposeStreamProvider<QueueStatus> {
  /// Real-time queue status provider for a specific queue
  ///
  /// Copied from [realtimeQueueStatus].
  RealtimeQueueStatusProvider(
    String queueId,
  ) : this._internal(
          (ref) => realtimeQueueStatus(
            ref as RealtimeQueueStatusRef,
            queueId,
          ),
          from: realtimeQueueStatusProvider,
          name: r'realtimeQueueStatusProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$realtimeQueueStatusHash,
          dependencies: RealtimeQueueStatusFamily._dependencies,
          allTransitiveDependencies:
              RealtimeQueueStatusFamily._allTransitiveDependencies,
          queueId: queueId,
        );

  RealtimeQueueStatusProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.queueId,
  }) : super.internal();

  final String queueId;

  @override
  Override overrideWith(
    Stream<QueueStatus> Function(RealtimeQueueStatusRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: RealtimeQueueStatusProvider._internal(
        (ref) => create(ref as RealtimeQueueStatusRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        queueId: queueId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<QueueStatus> createElement() {
    return _RealtimeQueueStatusProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is RealtimeQueueStatusProvider && other.queueId == queueId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, queueId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin RealtimeQueueStatusRef on AutoDisposeStreamProviderRef<QueueStatus> {
  /// The parameter `queueId` of this provider.
  String get queueId;
}

class _RealtimeQueueStatusProviderElement
    extends AutoDisposeStreamProviderElement<QueueStatus>
    with RealtimeQueueStatusRef {
  _RealtimeQueueStatusProviderElement(super.provider);

  @override
  String get queueId => (origin as RealtimeQueueStatusProvider).queueId;
}

String _$realtimeNotificationsHash() =>
    r'ccda9b56dd4bc64cc6980a18b61e73a03653f8bf';

/// Real-time notifications provider
///
/// Copied from [realtimeNotifications].
@ProviderFor(realtimeNotifications)
final realtimeNotificationsProvider =
    AutoDisposeStreamProvider<List<AppNotification>>.internal(
  realtimeNotifications,
  name: r'realtimeNotificationsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$realtimeNotificationsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef RealtimeNotificationsRef
    = AutoDisposeStreamProviderRef<List<AppNotification>>;
String _$realtimeNewNotificationHash() =>
    r'f423e9cb08f9bfc25a97a302bcfa44ed016d303d';

/// Real-time new notification provider
///
/// Copied from [realtimeNewNotification].
@ProviderFor(realtimeNewNotification)
final realtimeNewNotificationProvider =
    AutoDisposeStreamProvider<AppNotification>.internal(
  realtimeNewNotification,
  name: r'realtimeNewNotificationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$realtimeNewNotificationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef RealtimeNewNotificationRef
    = AutoDisposeStreamProviderRef<AppNotification>;
String _$realtimeUnreadNotificationsCountHash() =>
    r'638f76d4b9555b5a103f66918eec06dd84b8fb22';

/// Real-time unread notifications count provider
///
/// Copied from [realtimeUnreadNotificationsCount].
@ProviderFor(realtimeUnreadNotificationsCount)
final realtimeUnreadNotificationsCountProvider =
    AutoDisposeStreamProvider<int>.internal(
  realtimeUnreadNotificationsCount,
  name: r'realtimeUnreadNotificationsCountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$realtimeUnreadNotificationsCountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef RealtimeUnreadNotificationsCountRef = AutoDisposeStreamProviderRef<int>;
String _$initializeRealtimeServicesHash() =>
    r'd358e4aa8e54626ef7c62e5889efd1af850c7e16';

/// Provider for initializing all real-time services
///
/// Copied from [initializeRealtimeServices].
@ProviderFor(initializeRealtimeServices)
final initializeRealtimeServicesProvider =
    AutoDisposeFutureProvider<void>.internal(
  initializeRealtimeServices,
  name: r'initializeRealtimeServicesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$initializeRealtimeServicesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef InitializeRealtimeServicesRef = AutoDisposeFutureProviderRef<void>;
String _$conversationRoomManagerHash() =>
    r'dba150bc6ff0ac296f17431046fefd0bb831fa3b';

/// Provider for managing conversation room subscriptions
///
/// Copied from [ConversationRoomManager].
@ProviderFor(ConversationRoomManager)
final conversationRoomManagerProvider =
    AutoDisposeNotifierProvider<ConversationRoomManager, Set<String>>.internal(
  ConversationRoomManager.new,
  name: r'conversationRoomManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$conversationRoomManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConversationRoomManager = AutoDisposeNotifier<Set<String>>;
String _$queueStatusManagerHash() =>
    r'0d399706964d2f32b3b9e4567bad6e3acec33877';

/// Provider for managing queue status requests
///
/// Copied from [QueueStatusManager].
@ProviderFor(QueueStatusManager)
final queueStatusManagerProvider =
    AutoDisposeNotifierProvider<QueueStatusManager, Set<String>>.internal(
  QueueStatusManager.new,
  name: r'queueStatusManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$queueStatusManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$QueueStatusManager = AutoDisposeNotifier<Set<String>>;
String _$realtimeServiceHealthHash() =>
    r'b568e8c563869e83022f2f377de268e9d5479e78';

/// Provider for real-time service health monitoring
///
/// Copied from [RealtimeServiceHealth].
@ProviderFor(RealtimeServiceHealth)
final realtimeServiceHealthProvider = AutoDisposeNotifierProvider<
    RealtimeServiceHealth, Map<String, bool>>.internal(
  RealtimeServiceHealth.new,
  name: r'realtimeServiceHealthProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$realtimeServiceHealthHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RealtimeServiceHealth = AutoDisposeNotifier<Map<String, bool>>;
String _$connectionRetryManagerHash() =>
    r'61b2d07f1c50162087c6b8caee2b588a45dc8f86';

/// Provider for connection retry management
///
/// Copied from [ConnectionRetryManager].
@ProviderFor(ConnectionRetryManager)
final connectionRetryManagerProvider =
    AutoDisposeNotifierProvider<ConnectionRetryManager, int>.internal(
  ConnectionRetryManager.new,
  name: r'connectionRetryManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$connectionRetryManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConnectionRetryManager = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
