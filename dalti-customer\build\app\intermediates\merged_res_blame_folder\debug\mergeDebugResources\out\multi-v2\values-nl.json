{"logs": [{"outputFile": "org.adscloud.dalti.customer.app-mergeDebugResources-51:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\91df1ec7fb930f87f9c88e367152244b\\transformed\\preference-1.2.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,267,348,494,663,743", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "172,262,343,489,658,738,815"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5864,6039,6440,6521,6850,7019,7099", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "5931,6124,6516,6662,7014,7094,7171"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fd831584ed04ca4fba5608ab323cf35f\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,444,568,675,838,961,1080,1182,1356,1458,1623,1745,1904,2082,2146,2205", "endColumns": "103,146,123,106,162,122,118,101,173,101,164,121,158,177,63,58,74", "endOffsets": "296,443,567,674,837,960,1079,1181,1355,1457,1622,1744,1903,2081,2145,2204,2279"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3566,3674,3825,3953,4064,4231,4358,4481,4730,4908,5014,5183,5309,5472,5654,5722,5785", "endColumns": "107,150,127,110,166,126,122,105,177,105,168,125,162,181,67,62,78", "endOffsets": "3669,3820,3948,4059,4226,4353,4476,4582,4903,5009,5178,5304,5467,5649,5717,5780,5859"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e83d8c5cd0d2af91e865bb02d93869ba\\transformed\\appcompat-1.1.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,521,629,749,827,904,996,1089,1184,1278,1379,1473,1569,1664,1756,1848,1929,2040,2143,2242,2357,2471,2574,2729,2832", "endColumns": "117,104,106,85,107,119,77,76,91,92,94,93,100,93,95,94,91,91,80,110,102,98,114,113,102,154,102,81", "endOffsets": "218,323,430,516,624,744,822,899,991,1084,1179,1273,1374,1468,1564,1659,1751,1843,1924,2035,2138,2237,2352,2466,2569,2724,2827,2909"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,521,629,749,827,904,996,1089,1184,1278,1379,1473,1569,1664,1756,1848,1929,2040,2143,2242,2357,2471,2574,2729,6667", "endColumns": "117,104,106,85,107,119,77,76,91,92,94,93,100,93,95,94,91,91,80,110,102,98,114,113,102,154,102,81", "endOffsets": "218,323,430,516,624,744,822,899,991,1084,1179,1273,1374,1468,1564,1659,1751,1843,1924,2035,2138,2237,2352,2466,2569,2724,2827,6744"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\46a2f623aa225cef07cf72877fccfef1\\transformed\\browser-1.8.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5936,6129,6230,6341", "endColumns": "102,100,110,98", "endOffsets": "6034,6225,6336,6435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fd004af28618635ed2c68077f32c9eb6\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4587", "endColumns": "142", "endOffsets": "4725"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e5672b6e30b06127e0c3f67c9f39e57a\\transformed\\core-1.16.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2832,2934,3036,3136,3236,3343,3447,6749", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "2929,3031,3131,3231,3338,3442,3561,6845"}}]}]}