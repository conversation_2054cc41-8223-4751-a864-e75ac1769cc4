# Enhanced PowerShell script to remove print statements AND developer.log statements
param(
    [string]$LibPath = "lib",
    [switch]$DryRun = $false
)

Write-Host "Starting comprehensive logging cleanup..."
Write-Host "Target directory: $LibPath"
Write-Host "Dry run mode: $DryRun"

$dartFiles = Get-ChildItem -Path $LibPath -Recurse -Filter "*.dart"
Write-Host "Found $($dartFiles.Count) Dart files"

$totalPrintRemoved = 0
$totalDeveloperLogRemoved = 0
$filesProcessed = 0

foreach ($file in $dartFiles) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Count existing print statements
    $printMatches = [regex]::Matches($content, "print\s*\(")
    $printCount = $printMatches.Count
    
    # Count existing developer.log statements
    $devLogMatches = [regex]::Matches($content, "developer\.log\s*\(")
    $devLogCount = $devLogMatches.Count
    
    $totalLogsInFile = $printCount + $devLogCount
    
    if ($totalLogsInFile -gt 0) {
        Write-Host "Processing: $($file.Name) - $printCount prints, $devLogCount developer.logs"
        
        # Remove print statements using regex
        # Handle single line prints
        $content = [regex]::Replace($content, "(?m)^\s*print\s*\([^;]*\);\s*$", "")
        
        # Handle multi-line prints (basic pattern)
        $content = [regex]::Replace($content, "(?ms)^\s*print\s*\(\s*\n.*?\n\s*\);\s*$", "")
        
        # Remove developer.log statements
        # Handle single line developer.log
        $content = [regex]::Replace($content, "(?m)^\s*developer\.log\s*\([^;]*\);\s*$", "")
        
        # Handle multi-line developer.log (basic pattern)
        $content = [regex]::Replace($content, "(?ms)^\s*developer\.log\s*\(\s*\n.*?\n\s*\);\s*$", "")
        
        # Clean up extra empty lines
        $content = [regex]::Replace($content, "(?m)^\s*\n\s*\n\s*\n", "`n`n")
        $content = [regex]::Replace($content, "(?m)^\s*\n\s*\n\s*\n", "`n`n")
        
        if (-not $DryRun) {
            Set-Content -Path $file.FullName -Value $content -NoNewline
        }
        
        $totalPrintRemoved += $printCount
        $totalDeveloperLogRemoved += $devLogCount
        $filesProcessed++
    }
}

Write-Host "COMPLETED!"
Write-Host "Files processed: $filesProcessed"
Write-Host "Print statements removed: $totalPrintRemoved"
Write-Host "Developer.log statements removed: $totalDeveloperLogRemoved"
Write-Host "Total logging statements removed: $($totalPrintRemoved + $totalDeveloperLogRemoved)"

if ($DryRun) {
    Write-Host "This was a dry run - no files were modified"
}
