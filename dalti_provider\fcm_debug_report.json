{"timestamp": "2025-09-06T14:22:46.981Z", "success": ["✅ FCM permission found in manifest", "✅ Notification permission found in manifest", "✅ FCM service intent filter found", "✅ Notification channel configured: dalti_provider_notifications", "✅ Default notification icon configured", "✅ Firebase project: dalti-prod", "✅ Package name: org.adscloud.dalti.provider", "✅ Firebase messaging sender ID configured", "✅ Firebase project ID configured", "✅ Service uses correct channel ID: dalti_provider_notifications", "✅ Foreground message handler configured", "✅ Local notifications plugin integrated", "✅ Notification channel creation implemented", "✅ Firebase initialization found in main.dart", "✅ Background message handler registered in main.dart", "✅ FCM service initialization found", "✅ Dependency found: firebase_core", "✅ Dependency found: firebase_messaging", "✅ Dependency found: flutter_local_notifications"], "warnings": ["⚠️  Background message handler not found in service"], "issues": [], "summary": {"successCount": 19, "warningCount": 1, "issueCount": 0, "status": "PASS"}}