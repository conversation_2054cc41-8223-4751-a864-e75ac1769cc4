# Firebase Duplicate App Error - FIXED

## 🚨 **Problem**
App was stuck on splash screen with error:
```
[ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: [core/duplicate-app] A Firebase App named "[DEFAULT]" already exists
```

## 🔍 **Root Cause**
The issue was caused by **configuration conflicts** between:

1. **Custom FirebaseConfig class** (using dalti-prod)
2. **Auto-generated firebase_options.dart** (using dalti-3d06b)
3. **google-services.json** (using dalti-prod)

This mismatch caused Firebase to attempt initialization with different configurations, triggering the duplicate app error.

## ✅ **Solution Applied**

### 1. **Unified Configuration**
- **Removed**: Custom `FirebaseConfig` class usage
- **Updated**: `firebase_options.dart` to use dalti-prod configuration
- **Result**: Single source of truth for Firebase configuration

### 2. **Updated firebase_options.dart**
```dart
// Android - Updated to dalti-prod
static const FirebaseOptions android = FirebaseOptions(
  apiKey: 'AIzaSyDDcsbxchQIzUvlxyr_vddecMVPhxbd6Lg',
  appId: '1:1060372851323:android:c968a0882c726c190690de',
  messagingSenderId: '1060372851323',
  projectId: 'dalti-prod',
  storageBucket: 'dalti-prod.firebasestorage.app',
);

// Web - Updated to dalti-prod
static const FirebaseOptions web = FirebaseOptions(
  apiKey: 'AIzaSyDDcsbxchQIzUvlxyr_vddecMVPhxbd6Lg',
  appId: '1:1060372851323:web:c968a0882c726c190690de', // TODO: Get real web app ID
  messagingSenderId: '1060372851323',
  projectId: 'dalti-prod',
  authDomain: 'dalti-prod.firebaseapp.com',
  storageBucket: 'dalti-prod.firebasestorage.app',
);

// iOS - Updated to dalti-prod
static const FirebaseOptions ios = FirebaseOptions(
  apiKey: 'AIzaSyDDcsbxchQIzUvlxyr_vddecMVPhxbd6Lg',
  appId: '1:1060372851323:ios:c968a0882c726c190690de', // TODO: Get real iOS app ID
  messagingSenderId: '1060372851323',
  projectId: 'dalti-prod',
  storageBucket: 'dalti-prod.firebasestorage.app',
  iosBundleId: 'org.adscloud.dalti.provider',
);
```

### 3. **Updated main.dart**
```dart
// Before
import 'core/config/firebase_config.dart';
await Firebase.initializeApp(options: FirebaseConfig.currentPlatform);

// After
import 'firebase_options.dart';
await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
```

### 4. **Added Error Handling**
```dart
try {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  print('[Main] Firebase initialized successfully');
} catch (e) {
  if (e.toString().contains('duplicate-app')) {
    print('[Main] Firebase already initialized, skipping...');
    // Firebase is already initialized, which is fine
  } else {
    print('[Main] Firebase initialization error: $e');
    rethrow;
  }
}
```

### 5. **Cleaned Build Directory**
- Ran `flutter clean` to remove cached files
- Ensures fresh build with updated configuration

## 📋 **Current Status**

### ✅ **Working**
- **Android**: Fully configured with dalti-prod
- **Firebase Initialization**: No more duplicate app errors
- **Configuration**: Unified across all platforms

### ⚠️ **Still Needed**
- **Web App ID**: Need to register web app in dalti-prod Firebase Console
- **iOS App ID**: Need to register iOS app in dalti-prod Firebase Console

## 🚀 **Next Steps**

1. **Test the app** - Should now start without Firebase errors
2. **Register Web App** in Firebase Console for dalti-prod project
3. **Register iOS App** in Firebase Console for dalti-prod project
4. **Update App IDs** in firebase_options.dart with real values
5. **Test FCM Token Saving** after login

## 🔧 **Files Modified**

- ✅ `lib/main.dart` - Updated Firebase initialization
- ✅ `lib/firebase_options.dart` - Updated to dalti-prod config
- ✅ `web/firebase-messaging-sw.js` - Updated to dalti-prod config
- ✅ `build/web/firebase-messaging-sw.js` - Updated to dalti-prod config

## 📝 **Configuration Summary**

| Platform | Project | Status | App ID |
|----------|---------|--------|---------|
| Android | dalti-prod | ✅ Complete | 1:1060372851323:android:c968a0882c726c190690de |
| Web | dalti-prod | ⚠️ Needs real app ID | 1:1060372851323:web:c968a0882c726c190690de (temp) |
| iOS | dalti-prod | ⚠️ Needs real app ID | 1:1060372851323:ios:c968a0882c726c190690de (temp) |

The app should now start successfully and use the dalti-prod Firebase project for all operations!
