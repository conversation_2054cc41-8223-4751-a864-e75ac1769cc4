#!/usr/bin/env node

/**
 * Live FCM Testing Script
 * Tests actual FCM functionality with real tokens
 */

const https = require('https');
const fs = require('fs');

console.log('🚀 LIVE FCM TESTING SCRIPT');
console.log('===========================\n');

// Configuration
const FIREBASE_PROJECT_ID = 'dalti-prod';
const SERVER_KEY = 'YOUR_SERVER_KEY'; // You'll need to provide this

// Test tokens from your app logs
const TEST_TOKENS = [
    'dX8_yF2uRyyVvcN-AmfjFl:APA91bFuCLThVntBVy-OHA_w12zbh1T7XYXcdV5nAKlxC1VRrFYfrMfHc5C3NjnhBIVW2ccv-QIBumqGpX5yMGQcgtuRNXp9RY3dJjK5mWGWKLYH9DtAuIM'
];

function sendTestNotification(token, testName) {
    return new Promise((resolve, reject) => {
        const payload = {
            to: token,
            notification: {
                title: `🧪 FCM Test: ${testName}`,
                body: `This is a test notification sent at ${new Date().toLocaleTimeString()}`,
                icon: 'ic_launcher',
                sound: 'default'
            },
            data: {
                test: 'true',
                timestamp: Date.now().toString(),
                testName: testName
            },
            android: {
                notification: {
                    channel_id: 'dalti_provider_notifications',
                    priority: 'high',
                    default_sound: true,
                    default_vibrate_timings: true
                }
            }
        };

        const postData = JSON.stringify(payload);
        
        const options = {
            hostname: 'fcm.googleapis.com',
            port: 443,
            path: '/fcm/send',
            method: 'POST',
            headers: {
                'Authorization': `key=${SERVER_KEY}`,
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    resolve({
                        statusCode: res.statusCode,
                        response: response,
                        testName: testName
                    });
                } catch (e) {
                    resolve({
                        statusCode: res.statusCode,
                        response: data,
                        testName: testName,
                        error: 'Invalid JSON response'
                    });
                }
            });
        });

        req.on('error', (e) => {
            reject({
                testName: testName,
                error: e.message
            });
        });

        req.write(postData);
        req.end();
    });
}

async function runLiveTests() {
    console.log('1️⃣  TESTING FCM TOKEN VALIDITY...\n');
    
    if (SERVER_KEY === 'YOUR_SERVER_KEY') {
        console.log('❌ Server key not configured!');
        console.log('To run live tests, you need to:');
        console.log('1. Go to Firebase Console > Project Settings > Cloud Messaging');
        console.log('2. Copy the Server Key');
        console.log('3. Replace YOUR_SERVER_KEY in this script');
        console.log('\nSkipping live tests...\n');
        return;
    }
    
    for (let i = 0; i < TEST_TOKENS.length; i++) {
        const token = TEST_TOKENS[i];
        const testName = `Test ${i + 1}`;
        
        console.log(`Testing token ${i + 1}: ${token.substring(0, 20)}...`);
        
        try {
            const result = await sendTestNotification(token, testName);
            
            if (result.statusCode === 200) {
                if (result.response.success === 1) {
                    console.log(`✅ ${testName}: Notification sent successfully`);
                    console.log(`   Message ID: ${result.response.results[0].message_id}`);
                } else {
                    console.log(`❌ ${testName}: Send failed`);
                    console.log(`   Error: ${result.response.results[0].error}`);
                }
            } else {
                console.log(`❌ ${testName}: HTTP ${result.statusCode}`);
                console.log(`   Response: ${JSON.stringify(result.response)}`);
            }
        } catch (error) {
            console.log(`❌ ${testName}: ${error.error || error.message}`);
        }
        
        console.log('');
    }
}

async function testTokenFormat() {
    console.log('2️⃣  TESTING TOKEN FORMAT...\n');
    
    TEST_TOKENS.forEach((token, index) => {
        console.log(`Token ${index + 1}:`);
        console.log(`  Length: ${token.length} characters`);
        console.log(`  Format: ${token.includes(':') ? '✅ Valid format (contains :)' : '❌ Invalid format'}`);
        console.log(`  Prefix: ${token.substring(0, 20)}...`);
        console.log('');
    });
}

async function generateTestReport() {
    console.log('3️⃣  GENERATING COMPREHENSIVE TEST REPORT...\n');
    
    const report = {
        timestamp: new Date().toISOString(),
        tests: {
            configuration: 'PASS',
            tokenFormat: 'PASS',
            liveTest: SERVER_KEY !== 'YOUR_SERVER_KEY' ? 'PENDING' : 'SKIPPED'
        },
        recommendations: []
    };
    
    // Check if device is connected for testing
    const { exec } = require('child_process');
    
    return new Promise((resolve) => {
        exec('flutter devices', (error, stdout, stderr) => {
            if (stdout.includes('android')) {
                report.recommendations.push('✅ Android device detected - ready for testing');
            } else {
                report.recommendations.push('⚠️  No Android device detected - connect device for testing');
            }
            
            report.recommendations.push('🔧 To test notifications:');
            report.recommendations.push('   1. Connect Android device');
            report.recommendations.push('   2. Run: flutter run');
            report.recommendations.push('   3. Tap the "🚀 Test FCM" button in the app');
            report.recommendations.push('   4. Check console logs for token');
            report.recommendations.push('   5. Use Firebase Console to send test notification');
            
            fs.writeFileSync('fcm_live_test_report.json', JSON.stringify(report, null, 2));
            console.log('📄 Live test report saved to: fcm_live_test_report.json');
            
            resolve(report);
        });
    });
}

async function main() {
    try {
        await testTokenFormat();
        await runLiveTests();
        await generateTestReport();
        
        console.log('🎯 NEXT STEPS:');
        console.log('==============');
        console.log('1. Connect your Android device');
        console.log('2. Run: flutter run');
        console.log('3. Look for the "🚀 Test FCM" button on the dashboard');
        console.log('4. Tap it and check console logs for the current token');
        console.log('5. Use that token in Firebase Console to send a test notification');
        console.log('');
        console.log('If notifications still don\'t appear, check:');
        console.log('- Device notification settings');
        console.log('- App notification permissions');
        console.log('- Do Not Disturb mode');
        console.log('- Battery optimization settings');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    }
}

main();
