// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'profile_completion_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$profileCompletionServiceHash() =>
    r'b921a61edd11889ef78c1c298c666b2ef0642b70';

/// Provider for ProfileCompletionService
///
/// Copied from [profileCompletionService].
@ProviderFor(profileCompletionService)
final profileCompletionServiceProvider =
    AutoDisposeProvider<ProfileCompletionService>.internal(
  profileCompletionService,
  name: r'profileCompletionServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$profileCompletionServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ProfileCompletionServiceRef
    = AutoDisposeProviderRef<ProfileCompletionService>;
String _$profileCompletionNotifierHash() =>
    r'cced8ae0d184715c78c5f202c855ee45d37dd1fe';

/// Profile completion provider
///
/// Copied from [ProfileCompletionNotifier].
@ProviderFor(ProfileCompletionNotifier)
final profileCompletionNotifierProvider = AutoDisposeNotifierProvider<
    ProfileCompletionNotifier, ProfileCompletionState>.internal(
  ProfileCompletionNotifier.new,
  name: r'profileCompletionNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$profileCompletionNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProfileCompletionNotifier
    = AutoDisposeNotifier<ProfileCompletionState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
