const https = require('https');

// Firebase Admin SDK Configuration
const FIREBASE_CONFIG = {
  "type": "service_account",
  "project_id": "dalti-prod",
  "private_key_id": "your_private_key_id",
  "private_key": "-----B<PERSON>IN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCmPAhimjq+NqOx\n3tKbnaIUURs0HhPUzIuG+zXbT5c73i5r09fIC9hPkyxk+eSP2JcNnO9DiWn2m+jr\nA+C+Ul6ls7Ykd5TO3wAvxdlRW5vVozegva4IaaoAuu0WCub2XEMm1eCeNL4HmOv6\njT/w7lFxCQvoJHdBNn2WtlUqi8If5lDTBaCLkhYzuZaEqWtKFH6+jugfNLMbCytV\nvJuMOcR8WCc5ulbiqF3rd2F40lD4iVRfEWG5EQncrYcBC4/2pZO+T3Y4TK/xdfmN\n1ziPGmKf5WXNzxVHtWBy6ubjzYIQj5TH6HJaOpd9+EzGMWS/DUEOca/X7RH3qysB\nUPaek7jZAgMBAAECggEAFe0kh1T5MJkRExIP94Sy7zkaJyVcekdsk/uonpUrSTNE\n2OYoDR6QLpbRVpRQ3dm0uuVF+JjfHYRyaXqpIebIIypxePKTnIx01YL5ki33f8C3\n7VTM6BZ/3Pl1nOBdQduTjpjMDYdpaqPKDydNwCAUep4LMFiMU6a1/rNtcl+IKyAa\n1BMftcSTr6TdhtbEBNYW4T2fWR5VoP5b6mhpRdstD7DLNdhEs4yC4nGFjvbZML1a\nrfETK7oL43LHv83HWPf0scVkcrKBkRTKyPMtpWh6sV5FYFdMOtpyAQTaCrDsX+9l\nUD0f50oWUqjgHdcm0SUIfRvOb9Axrdu+EG6nPU2RHQKBgQDm689811dTpYK5Fl+9\n4T1CoJXNe7n/AZmbioVKq9KzhUPCgW/xHHI1/tIXmVvHw4HT0sWigFtkx1rGlIn+\nUZkth/RGdeYj0s9Kor2dottS72Efrna6ufQIyOmAljvnceEQ/+57CJNcE+F8Qf9x\neQ5nv+1oe3lJr77obztHl+OJWwKBgQC4ScVX2VaVnQ4C4ezCoqfM5Nnc3q3EUY3Y\ns9ISjJ51TdDVz0b5xktJvu2bmGUv3x7WBvo5WU8ZrADPxEnW3KfvqZ/MKYfMAtKE\nYSK/lBRVhAvWkze4mI30hRH7asUcrCIz1QOB+RNjCV8KrahhSx0Jg+7bJm+coJEy\nZ9o/JxUo2wKBgQDZie5mckrvHu2+RWiebVETskpNULCQncZCdEVLagDNY87Irr1x\nC9ZALbVny+5diz2D+nx6sKbokrFef9Jbxi0iaoBh3HGY7+CeNB+jICezwpThjq6F\nC+bsW7E1lNIMIAjhxei4+QQxav0x3M6y7FL4xCL4GdHR7AFR4G+c62bMHwKBgCZa\n9D+8dFDfRzNCYkyAHfx/BPj7P59EeglII8jIi5JOh9B4O4Vwx+qpWjqwFR9JiNly\nylF1TQlCy0hyygt2EV3IKAIOAr1rOPVkYh8Nas4BlZUrPsWkuqbRq1RwfGXjTZsM\nEpbSiD5sjONkEU9umrSISQZDkac/o/ihtMLAF59RAoGACgMQ1BlKq9uv9j6YHhlT\nahnM+dTn72gWJy0hBX6D4Hf+pRo7vo4KTSB1tsD8DWNNKNVje0yZz0CQDFG9Hvma\nPDR4TlFKQ3K40/vRlHF0wf/w33KP9NmMZITZKiMFDeMZgvTfA98D/xscT43Hft1G\n+vBdGoUesoHjfqB6EKaLwW0=\n-----END PRIVATE KEY-----",
  "client_email": "<EMAIL>",
  "client_id": "123456789012345678901",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token"
};

// Your FCM Token
const FCM_TOKEN = "dW5jfBcDT5OYiLwwa9kTOQ:APA91bF_mxEhW2jcS-mdCLWfCdbPrFMmvl3TwzCPKENcvEnRhmYyB_bNr6adjKlOgj6UhZyd87AxXhwYhSxVv7kX22OgXD_vZvotCmKw57LqiBHIKW5tr4A";

// Function to get OAuth2 access token
async function getAccessToken() {
  const jwt = require('jsonwebtoken');
  
  const now = Math.floor(Date.now() / 1000);
  const payload = {
    iss: FIREBASE_CONFIG.client_email,
    scope: 'https://www.googleapis.com/auth/firebase.messaging',
    aud: 'https://oauth2.googleapis.com/token',
    iat: now,
    exp: now + 3600
  };
  
  const token = jwt.sign(payload, FIREBASE_CONFIG.private_key, { algorithm: 'RS256' });
  
  return new Promise((resolve, reject) => {
    const postData = `grant_type=urn:ietf:params:oauth:grant-type:jwt-bearer&assertion=${token}`;
    
    const options = {
      hostname: 'oauth2.googleapis.com',
      port: 443,
      path: '/token',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve(response.access_token);
        } catch (error) {
          reject(error);
        }
      });
    });
    
    req.on('error', reject);
    req.write(postData);
    req.end();
  });
}

// Function to send FCM notification
async function sendTestNotification() {
  try {
    console.log('🔥 Getting Firebase access token...');
    const accessToken = await getAccessToken();
    console.log('✅ Access token obtained');
    
    const message = {
      message: {
        token: FCM_TOKEN,
        notification: {
          title: "🚀 Test from API",
          body: "Hello! This is a test notification sent directly from Firebase Admin SDK API"
        },
        data: {
          test_key: "test_value",
          timestamp: new Date().toISOString()
        },
        android: {
          priority: "high",
          notification: {
            channel_id: "dalti_provider_notifications",
            icon: "launcher_icon",
            color: "#15424E"
          }
        }
      }
    };
    
    const postData = JSON.stringify(message);
    
    const options = {
      hostname: 'fcm.googleapis.com',
      port: 443,
      path: '/v1/projects/dalti-prod/messages:send',
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    console.log('📤 Sending notification...');
    
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        console.log(`📊 Response Status: ${res.statusCode}`);
        console.log('📋 Response Headers:', res.headers);
        console.log('📄 Response Body:', data);
        
        if (res.statusCode === 200) {
          console.log('🎉 SUCCESS! Notification sent successfully!');
          console.log('👀 Check your app now - you should see the notification!');
        } else {
          console.log('❌ FAILED! Error sending notification');
        }
      });
    });
    
    req.on('error', (error) => {
      console.error('❌ Request error:', error);
    });
    
    req.write(postData);
    req.end();
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run the test
console.log('🔥 Firebase FCM Test Starting...');
console.log('📱 Target Token:', FCM_TOKEN.substring(0, 20) + '...');
console.log('🎯 Project ID: dalti-prod');
console.log('');

sendTestNotification();
