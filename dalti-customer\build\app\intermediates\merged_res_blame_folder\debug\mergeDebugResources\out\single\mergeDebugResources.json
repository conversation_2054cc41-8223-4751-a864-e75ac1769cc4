[{"merged": "org.adscloud.dalti.customer.app-debug-53:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "org.adscloud.dalti.customer.app-main-47:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "org.adscloud.dalti.customer.app-debug-53:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "org.adscloud.dalti.customer.app-main-47:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "org.adscloud.dalti.customer.app-debug-53:/drawable-v21_launch_background.xml.flat", "source": "org.adscloud.dalti.customer.app-main-47:/drawable-v21/launch_background.xml"}, {"merged": "org.adscloud.dalti.customer.app-debug-53:/mipmap-xhdpi_ic_launcher.png.flat", "source": "org.adscloud.dalti.customer.app-main-47:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "org.adscloud.dalti.customer.app-debug-53:/xml_network_security_config.xml.flat", "source": "org.adscloud.dalti.customer.app-main-47:/xml/network_security_config.xml"}, {"merged": "org.adscloud.dalti.customer.app-debug-53:/mipmap-hdpi_ic_launcher.png.flat", "source": "org.adscloud.dalti.customer.app-main-47:/mipmap-hdpi/ic_launcher.png"}, {"merged": "org.adscloud.dalti.customer.app-debug-53:/mipmap-mdpi_ic_launcher.png.flat", "source": "org.adscloud.dalti.customer.app-main-47:/mipmap-mdpi/ic_launcher.png"}, {"merged": "org.adscloud.dalti.customer.app-debug-53:/xml_file_paths.xml.flat", "source": "org.adscloud.dalti.customer.app-main-47:/xml/file_paths.xml"}]