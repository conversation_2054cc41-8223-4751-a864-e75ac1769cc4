<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_color">#2A9D8F</color>
    <color name="background_dark">#264653</color>
    <color name="background_light">#F7F5F2</color>
    <color name="notification_color">#15424E</color>
    <color name="primary_color">#15424E</color>
    <color name="provider_primary">#15424E</color>
    <color name="provider_secondary">#2A9D8F</color>
    <color name="status_bar_color">#15424E</color>
    <string name="gcm_defaultSenderId" translatable="false">1060372851323</string>
    <string name="google_api_key" translatable="false">AIzaSyDDcsbxchQIzUvlxyr_vddecMVPhxbd6Lg</string>
    <string name="google_app_id" translatable="false">1:1060372851323:android:c968a0882c726c190690de</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyDDcsbxchQIzUvlxyr_vddecMVPhxbd6Lg</string>
    <string name="google_storage_bucket" translatable="false">dalti-prod.firebasestorage.app</string>
    <string name="project_id" translatable="false">dalti-prod</string>
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style>
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>
</resources>