#!/usr/bin/env node

/**
 * Test FCM using Legacy API with Server Key
 * This might work better than the HTTP v1 API
 */

const https = require('https');
const fs = require('fs');

console.log('🔥 FCM LEGACY API TEST');
console.log('======================\n');

const FCM_TOKEN = 'dhOs2G1nT2CnKHqReUsat9:APA91bESgbZRzYD7YCqX-2UIal6BTduYJE4icIuaZ1WopPanbH8taK6PeroXwLiMsVdVYmQwjCnPgj-9dwcdtMvd52r_QyfOueSiK3LzgTrBODLYCDG25pw';

// Get API key from google-services.json
let API_KEY = null;

try {
    const config = JSON.parse(fs.readFileSync('android/app/google-services.json', 'utf8'));
    API_KEY = config.client[0].api_key[0].current_key;
    
    console.log('✅ Configuration loaded:');
    console.log(`   Project ID: ${config.project_info.project_id}`);
    console.log(`   Project Number: ${config.project_info.project_number}`);
    console.log(`   API Key: ${API_KEY.substring(0, 20)}...`);
    console.log(`   FCM Token: ${FCM_TOKEN.substring(0, 30)}...\n`);
    
} catch (e) {
    console.log('❌ Could not load google-services.json:', e.message);
    process.exit(1);
}

// Send notification using Legacy FCM API
function sendNotificationLegacy() {
    return new Promise((resolve) => {
        console.log('📤 Sending notification via Legacy FCM API...');
        
        const payload = {
            to: FCM_TOKEN,
            notification: {
                title: "🚀 Legacy API Test",
                body: "Testing FCM delivery via Legacy API",
                icon: "launcher_icon",
                color: "#15424E",
                sound: "default"
            },
            data: {
                test_type: "legacy_api",
                timestamp: new Date().toISOString(),
                source: "legacy_script"
            },
            priority: "high",
            content_available: true
        };
        
        const postData = JSON.stringify(payload);
        
        const options = {
            hostname: 'fcm.googleapis.com',
            port: 443,
            path: '/fcm/send',
            method: 'POST',
            headers: {
                'Authorization': `key=${API_KEY}`,
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };
        
        console.log('🔗 Endpoint: https://fcm.googleapis.com/fcm/send');
        console.log('🔑 Authorization: key=' + API_KEY.substring(0, 20) + '...');
        console.log('📦 Payload:');
        console.log(JSON.stringify(payload, null, 2));
        console.log('\n📡 Sending request...\n');
        
        const req = https.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                console.log(`📊 HTTP Status: ${res.statusCode}`);
                console.log(`📥 Response: ${responseData}\n`);
                
                if (res.statusCode === 200) {
                    try {
                        const response = JSON.parse(responseData);
                        
                        if (response.success === 1) {
                            console.log('🎉 SUCCESS! Notification sent successfully!');
                            console.log('📱 Check your Android device NOW!');
                            console.log(`📋 Message ID: ${response.results[0].message_id}`);
                            console.log('');
                            console.log('✅ CONCLUSION: Legacy FCM API is working!');
                            console.log('✅ The issue was with HTTP v1 API authentication');
                            resolve(true);
                        } else {
                            console.log('❌ Notification failed to send');
                            console.log(`💥 Error: ${response.results[0].error}`);
                            
                            // Provide specific error guidance
                            const error = response.results[0].error;
                            switch (error) {
                                case 'InvalidRegistration':
                                    console.log('💡 SOLUTION: FCM token is invalid');
                                    console.log('   • Generate a new FCM token in your app');
                                    break;
                                case 'NotRegistered':
                                    console.log('💡 SOLUTION: FCM token is not registered');
                                    console.log('   • App might be uninstalled or token expired');
                                    break;
                                case 'MismatchSenderId':
                                    console.log('💡 SOLUTION: Sender ID mismatch');
                                    console.log('   • Check if app is using correct google-services.json');
                                    break;
                                default:
                                    console.log(`💡 Error details: ${error}`);
                            }
                            resolve(false);
                        }
                    } catch (e) {
                        console.log('❌ Invalid JSON response:', responseData);
                        resolve(false);
                    }
                } else if (res.statusCode === 404) {
                    console.log('❌ Legacy FCM API is deprecated for this project');
                    console.log('💡 This project requires HTTP v1 API');
                    console.log('🔧 The issue is likely with service account permissions');
                    resolve(false);
                } else {
                    console.log(`❌ HTTP Error ${res.statusCode}`);
                    console.log(`📥 Response: ${responseData}`);
                    resolve(false);
                }
            });
        });
        
        req.on('error', (error) => {
            console.log(`❌ Network error: ${error.message}`);
            resolve(false);
        });
        
        req.write(postData);
        req.end();
    });
}

// Main execution
async function main() {
    const success = await sendNotificationLegacy();
    
    console.log('\n📊 FINAL ANALYSIS');
    console.log('==================\n');
    
    if (success) {
        console.log('✅ Legacy FCM API works perfectly!');
        console.log('✅ Your FCM token is valid');
        console.log('✅ Project configuration is correct');
        console.log('✅ Device should have received notification\n');
        
        console.log('🔍 IF NOTIFICATION APPEARED:');
        console.log('   • FCM is working correctly');
        console.log('   • Firebase Console issue was authentication-related');
        console.log('   • Your app can receive notifications properly\n');
        
        console.log('🔍 IF NOTIFICATION DID NOT APPEAR:');
        console.log('   • API call succeeded but device settings blocking');
        console.log('   • Check device notification permissions');
        console.log('   • Verify app is running and handling FCM messages');
        
    } else {
        console.log('❌ Legacy FCM API also failed');
        console.log('🔧 This suggests a deeper configuration issue\n');
        
        console.log('🔍 POSSIBLE CAUSES:');
        console.log('   • FCM token is from wrong project');
        console.log('   • App not properly configured with Firebase');
        console.log('   • google-services.json mismatch');
        console.log('   • Firebase project settings issue');
    }
    
    console.log('\n🎯 NEXT STEPS:');
    if (success) {
        console.log('1. If notification appeared: Everything is working!');
        console.log('2. If no notification: Focus on device settings');
        console.log('3. Check Flutter console for message reception logs');
    } else {
        console.log('1. Verify app is using correct google-services.json');
        console.log('2. Rebuild and reinstall app completely');
        console.log('3. Generate fresh FCM token after rebuild');
        console.log('4. Check Firebase Console for project configuration');
    }
}

main().catch(console.error);
