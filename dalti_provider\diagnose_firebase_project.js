#!/usr/bin/env node

/**
 * Comprehensive Firebase Project Diagnosis
 * This will help identify the exact project configuration issue
 */

const fs = require('fs');

console.log('🔍 COMPREHENSIVE FIREBASE PROJECT DIAGNOSIS');
console.log('============================================\n');

function analyzeProjectConfiguration() {
    console.log('1️⃣  PROJECT CONFIGURATION ANALYSIS');
    console.log('===================================\n');
    
    // Analyze google-services.json
    if (fs.existsSync('android/app/google-services.json')) {
        const config = JSON.parse(fs.readFileSync('android/app/google-services.json', 'utf8'));
        
        console.log('📱 ANDROID APP CONFIGURATION:');
        console.log(`   Project ID: ${config.project_info.project_id}`);
        console.log(`   Project Number: ${config.project_info.project_number}`);
        console.log(`   Storage Bucket: ${config.project_info.storage_bucket}`);
        console.log(`   App ID: ${config.client[0].client_info.mobilesdk_app_id}`);
        console.log(`   Package Name: ${config.client[0].client_info.android_client_info.package_name}`);
        console.log(`   API Key: ${config.client[0].api_key[0].current_key.substring(0, 20)}...`);
        
        // Check for services configuration
        const services = config.client[0].services;
        console.log('\n📋 SERVICES CONFIGURATION:');
        
        if (services.firebase_messaging) {
            console.log('   ✅ Firebase Messaging: Configured');
            console.log(`      Status: ${services.firebase_messaging.status || 'Unknown'}`);
        } else {
            console.log('   ❌ Firebase Messaging: NOT configured');
        }
        
        if (services.appinvite_service) {
            console.log('   ✅ App Invite Service: Configured');
        }
        
        // Check OAuth clients (for SHA certificates)
        console.log('\n🔐 OAUTH CLIENTS:');
        if (config.client[0].oauth_client && config.client[0].oauth_client.length > 0) {
            console.log(`   ✅ OAuth clients configured: ${config.client[0].oauth_client.length}`);
            config.client[0].oauth_client.forEach((client, index) => {
                console.log(`      Client ${index + 1}: ${client.client_id || 'Unknown'}`);
            });
        } else {
            console.log('   ⚠️  No OAuth clients configured (SHA certificates missing)');
        }
        
    } else {
        console.log('❌ google-services.json not found');
    }
    
    console.log('\n');
}

function analyzeServiceAccount() {
    console.log('2️⃣  SERVICE ACCOUNT ANALYSIS');
    console.log('=============================\n');
    
    if (fs.existsSync('service-account-key.json')) {
        const serviceAccount = JSON.parse(fs.readFileSync('service-account-key.json', 'utf8'));
        
        console.log('🔑 SERVICE ACCOUNT DETAILS:');
        console.log(`   Type: ${serviceAccount.type}`);
        console.log(`   Project ID: ${serviceAccount.project_id}`);
        console.log(`   Client Email: ${serviceAccount.client_email}`);
        console.log(`   Client ID: ${serviceAccount.client_id}`);
        console.log(`   Private Key ID: ${serviceAccount.private_key_id}`);
        console.log(`   Auth URI: ${serviceAccount.auth_uri}`);
        console.log(`   Token URI: ${serviceAccount.token_uri}`);
        
        // Check if it's a Firebase Admin SDK service account
        if (serviceAccount.client_email.includes('firebase-adminsdk')) {
            console.log('   ✅ Firebase Admin SDK service account');
        } else {
            console.log('   ⚠️  Not a Firebase Admin SDK service account');
        }
        
    } else {
        console.log('❌ service-account-key.json not found');
    }
    
    console.log('\n');
}

function identifyIssues() {
    console.log('3️⃣  ISSUE IDENTIFICATION');
    console.log('=========================\n');
    
    console.log('🔍 BASED ON THE TEST RESULTS:');
    console.log('');
    console.log('❌ Legacy FCM API: 404 Not Found');
    console.log('   → This means Legacy API is completely disabled for this project');
    console.log('   → Google has migrated this project to HTTP v1 API only');
    console.log('');
    console.log('❌ HTTP v1 API: SenderId mismatch');
    console.log('   → This suggests authentication or project configuration issue');
    console.log('   → Service account might not have correct permissions');
    console.log('');
    console.log('❌ Firebase Console: Not working');
    console.log('   → Console uses same HTTP v1 API internally');
    console.log('   → Same authentication issue affects console');
    console.log('\n');
}

function provideSolutions() {
    console.log('4️⃣  COMPREHENSIVE SOLUTIONS');
    console.log('============================\n');
    
    console.log('🔧 SOLUTION 1: Service Account Permissions');
    console.log('   1. Go to Google Cloud Console: https://console.cloud.google.com/');
    console.log('   2. Select project: dalti-prod');
    console.log('   3. Go to IAM & Admin > IAM');
    console.log('   4. Find your service account: <EMAIL>');
    console.log('   5. Make sure it has these roles:');
    console.log('      • Firebase Admin SDK Administrator Service Agent');
    console.log('      • Firebase Cloud Messaging Admin');
    console.log('      • Cloud Messaging Admin');
    console.log('');
    
    console.log('🔧 SOLUTION 2: Enable Cloud Messaging API');
    console.log('   1. Go to Google Cloud Console > APIs & Services > Library');
    console.log('   2. Search for "Firebase Cloud Messaging API"');
    console.log('   3. Make sure it\'s enabled');
    console.log('   4. Also enable "Cloud Messaging API" if available');
    console.log('');
    
    console.log('🔧 SOLUTION 3: Regenerate Service Account');
    console.log('   1. Go to Firebase Console > Project Settings > Service Accounts');
    console.log('   2. Delete current service account key');
    console.log('   3. Generate new private key');
    console.log('   4. Replace service-account-key.json with new file');
    console.log('   5. Test again');
    console.log('');
    
    console.log('🔧 SOLUTION 4: Check Firebase Project Settings');
    console.log('   1. Go to Firebase Console > Project Settings > Cloud Messaging');
    console.log('   2. Verify Cloud Messaging API is enabled');
    console.log('   3. Check if there are any project-level restrictions');
    console.log('   4. Verify the Android app is properly registered');
    console.log('');
    
    console.log('🔧 SOLUTION 5: Alternative Testing Method');
    console.log('   1. Install Google Cloud CLI');
    console.log('   2. Run: gcloud auth login');
    console.log('   3. Run: gcloud config set project dalti-prod');
    console.log('   4. Run: gcloud auth application-default print-access-token');
    console.log('   5. Use the token in a manual curl test');
    console.log('');
}

function createManualTest() {
    console.log('5️⃣  MANUAL TESTING COMMANDS');
    console.log('============================\n');
    
    const FCM_TOKEN = 'dhOs2G1nT2CnKHqReUsat9:APA91bESgbZRzYD7YCqX-2UIal6BTduYJE4icIuaZ1WopPanbH8taK6PeroXwLiMsVdVYmQwjCnPgj-9dwcdtMvd52r_QyfOueSiK3LzgTrBODLYCDG25pw';
    
    console.log('📋 CURL TEST WITH GOOGLE CLOUD CLI:');
    console.log('');
    console.log('# Step 1: Get access token');
    console.log('gcloud auth application-default print-access-token');
    console.log('');
    console.log('# Step 2: Test with curl (replace YOUR_TOKEN)');
    console.log(`curl -X POST \\
  -H "Authorization: Bearer YOUR_TOKEN" \\
  -H "Content-Type: application/json" \\
  -d '{
    "message": {
      "token": "${FCM_TOKEN}",
      "notification": {
        "title": "🔧 Manual Test",
        "body": "Testing with Google Cloud CLI token"
      },
      "android": {
        "priority": "high",
        "notification": {
          "channel_id": "dalti_provider_notifications"
        }
      }
    }
  }' \\
  "https://fcm.googleapis.com/v1/projects/dalti-prod/messages:send"`);
    
    console.log('\n📋 EXPECTED RESULTS:');
    console.log('• If this works: Service account permissions issue');
    console.log('• If this fails: Project configuration issue');
    console.log('• If notification appears: Device is working correctly');
    console.log('');
}

// Main execution
function main() {
    analyzeProjectConfiguration();
    analyzeServiceAccount();
    identifyIssues();
    provideSolutions();
    createManualTest();
    
    console.log('🎯 IMMEDIATE NEXT STEPS:');
    console.log('1. Check service account permissions in Google Cloud Console');
    console.log('2. Verify Cloud Messaging API is enabled');
    console.log('3. Try manual curl test with Google Cloud CLI');
    console.log('4. If still failing, regenerate service account key');
    console.log('');
    console.log('💡 MOST LIKELY CAUSE: Service account missing FCM permissions');
}

main();
