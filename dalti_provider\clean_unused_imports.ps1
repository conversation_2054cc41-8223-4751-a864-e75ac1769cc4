# PowerShell script to remove unused dart:developer imports
param(
    [string]$LibPath = "lib",
    [switch]$DryRun = $false
)

Write-Host "Starting unused import cleanup..."
Write-Host "Target directory: $LibPath"
Write-Host "Dry run mode: $DryRun"

$dartFiles = Get-ChildItem -Path $LibPath -Recurse -Filter "*.dart"
$totalImportsRemoved = 0
$filesProcessed = 0

foreach ($file in $dartFiles) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Check if file has dart:developer import
    if ($content -match "import 'dart:developer'") {
        # Check if developer.log is actually used in the file
        $developerUsage = [regex]::Matches($content, "developer\.")
        
        if ($developerUsage.Count -eq 0) {
            Write-Host "Processing: $($file.Name) - removing unused dart:developer import"
            
            # Remove the import line
            $content = [regex]::Replace($content, "(?m)^import 'dart:developer'.*?;\s*$", "")
            
            # Clean up extra empty lines
            $content = [regex]::Replace($content, "(?m)^\s*\n\s*\n\s*\n", "`n`n")
            
            if (-not $DryRun) {
                Set-Content -Path $file.FullName -Value $content -NoNewline
            }
            
            $totalImportsRemoved++
            $filesProcessed++
        }
    }
}

Write-Host "COMPLETED!"
Write-Host "Files processed: $filesProcessed"
Write-Host "Unused dart:developer imports removed: $totalImportsRemoved"

if ($DryRun) {
    Write-Host "This was a dry run - no files were modified"
}
