{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-57:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b9ebe0379b63cf1a9d593989bc44e09a\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "85,86,87,88,233,234,441,443,444,445", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3868,3926,3992,4055,13788,13859,27841,27966,28033,28112", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3921,3987,4050,4112,13854,13926,27904,28028,28107,28176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37fe99c3dffcb6c3e42d067184fcf556\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "29,71,72,91,92,123,125,235,236,237,238,239,240,241,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,336,337,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,386,415,416,417,418,419,420,421,458,1996,1997,2002,2005,2010,2155,2156,2821,2866,3036,3071,3101,3134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2762,2834,4232,4297,6387,6509,13931,14001,14069,14141,14211,14272,14346,15589,15650,15711,15773,15837,15899,15960,16028,16128,16188,16254,16327,16396,16453,16505,17665,17737,17813,17878,17937,17996,18056,18116,18176,18236,18296,18356,18416,18476,18536,18596,18655,18715,18775,18835,18895,18955,19015,19075,19135,19195,19255,19314,19374,19434,19493,19552,19611,19670,19729,20297,20332,20918,20973,21036,21091,21149,21205,21263,21324,21387,21444,21495,21553,21603,21664,21721,21787,21821,21856,22940,24959,25026,25098,25167,25236,25310,25382,29090,129368,129485,129752,130045,130312,141803,141875,163754,165728,173563,175369,176369,177051", "endLines": "29,71,72,91,92,123,125,235,236,237,238,239,240,241,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,336,337,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,386,415,416,417,418,419,420,421,458,1996,2000,2002,2008,2010,2155,2156,2826,2875,3070,3091,3133,3139", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2829,2917,4292,4358,6451,6567,13996,14064,14136,14206,14267,14341,14414,15645,15706,15768,15832,15894,15955,16023,16123,16183,16249,16322,16391,16448,16500,16562,17732,17808,17873,17932,17991,18051,18111,18171,18231,18291,18351,18411,18471,18531,18591,18650,18710,18770,18830,18890,18950,19010,19070,19130,19190,19250,19309,19369,19429,19488,19547,19606,19665,19724,19783,20327,20362,20968,21031,21086,21144,21200,21258,21319,21382,21439,21490,21548,21598,21659,21716,21782,21816,21851,21886,23005,25021,25093,25162,25231,25305,25377,25465,29156,129480,129681,129857,130241,130436,141870,141937,163952,166024,175364,176045,177046,177213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0ee4b2ddb8cfeba85a790df9f0361f7\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "332,333,338,345,346,367,368,369,370,371", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "20110,20150,20367,20705,20760,21891,21945,21997,22046,22107", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "20145,20192,20405,20755,20802,21940,21992,22041,22102,22152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\45247d19153ad738e998ed6c9e75cbba\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "244,245,246,254,255,256,335,3528", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "14592,14651,14699,15366,15441,15517,20231,189641", "endLines": "244,245,246,254,255,256,335,3547", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14646,14694,14750,15436,15512,15584,20292,190431"}}, {"source": "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1567,1571", "startColumns": "4,4", "startOffsets": "100276,100457", "endLines": "1570,1573", "endColumns": "12,12", "endOffsets": "100452,100621"}}, {"source": "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\build\\app\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,138,242,352,472,574", "endColumns": "82,103,109,119,101,70", "endOffsets": "133,237,347,467,569,640"}, "to": {"startLines": "447,448,449,450,451,456", "startColumns": "4,4,4,4,4,4", "startOffsets": "28263,28346,28450,28560,28680,28966", "endColumns": "82,103,109,119,101,70", "endOffsets": "28341,28445,28555,28675,28777,29032"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\77e48b10927d0b4496e8b44d4a9392fc\\transformed\\jetified-lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "377", "startColumns": "4", "startOffsets": "22389", "endColumns": "49", "endOffsets": "22434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cd64ffe0741cf986e6f68d942d161be1\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "331,347,378,3092,3097", "startColumns": "4,4,4,4,4", "startOffsets": "20053,20807,22439,176050,176220", "endLines": "331,347,378,3096,3100", "endColumns": "56,64,63,24,24", "endOffsets": "20105,20867,22498,176215,176364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b8cf5e82e5c42aeb586ea0c7cf5dbb72\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,330,2264,2270,3627,3635,3650", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,19993,145610,145805,192700,192982,193596", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,330,2269,2274,3634,3649,3665", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,20048,145800,145958,192977,193591,194245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\87617f28411ca702fb74dfb3b4d42b44\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3597,3669,3741,3814,3871,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3664,3736,3809,3866,3924,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "4,27,28,59,60,61,63,64,65,66,67,69,70,74,75,77,78,79,80,81,82,83,84,89,90,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,129,130,131,132,134,135,136,139,140,142,143,144,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,242,243,247,248,249,250,251,252,253,283,284,285,286,287,288,289,290,326,327,328,329,334,342,343,348,372,379,380,382,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,457,462,463,464,465,466,467,475,476,480,484,488,493,499,506,510,514,519,523,527,531,535,539,543,549,553,559,563,569,573,578,582,585,589,595,599,605,609,615,618,622,626,630,634,638,639,640,641,644,647,650,653,657,658,659,660,661,664,666,668,670,675,676,680,686,690,691,693,705,706,710,716,720,721,722,726,753,757,758,762,790,962,988,1159,1185,1216,1224,1230,1246,1268,1273,1278,1288,1297,1306,1310,1317,1336,1343,1344,1353,1356,1359,1363,1367,1371,1374,1375,1380,1385,1395,1400,1407,1413,1414,1417,1421,1426,1428,1430,1433,1436,1438,1442,1445,1452,1455,1458,1462,1464,1468,1470,1472,1474,1478,1486,1494,1506,1512,1521,1524,1535,1538,1539,1544,1545,1574,1643,1713,1714,1724,1733,1885,1887,1891,1894,1897,1900,1903,1906,1909,1912,1916,1919,1922,1925,1929,1932,1936,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1962,1964,1965,1966,1967,1968,1969,1970,1971,1973,1974,1976,1977,1979,1981,1982,1984,1985,1986,1987,1988,1989,1991,1992,1993,1994,1995,2012,2014,2016,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2032,2033,2034,2035,2036,2037,2038,2040,2044,2048,2049,2050,2051,2052,2053,2057,2058,2059,2060,2062,2064,2066,2068,2070,2071,2072,2073,2075,2077,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2093,2094,2095,2096,2098,2100,2101,2103,2104,2106,2108,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2123,2124,2125,2126,2128,2129,2130,2131,2132,2134,2136,2138,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2160,2235,2238,2241,2244,2258,2275,2317,2320,2349,2376,2385,2449,2817,2838,2876,3014,3140,3164,3170,3199,3220,3344,3372,3378,3522,3548,3615,3686,3786,3806,3861,3873,3899", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2214,2278,2348,2409,2484,2607,2684,2972,3057,3190,3266,3342,3419,3497,3603,3709,3788,4117,4174,5034,5108,5183,5248,5314,5374,5435,5507,5580,5647,5715,5774,5833,5892,5951,6010,6064,6118,6171,6225,6279,6333,6778,6852,6931,7004,7149,7221,7293,7470,7527,7658,7732,7806,7932,8004,8077,8147,8218,8278,8339,8408,8477,8547,8621,8697,8761,8838,8914,8991,9056,9125,9202,9277,9346,9414,9491,9557,9618,9715,9780,9849,9948,10019,10078,10136,10193,10252,10316,10387,10459,10531,10603,10675,10742,10810,10878,10937,11000,11064,11154,11245,11305,11371,11438,11504,11574,11638,11691,11758,11819,11886,11999,12057,12120,12185,12250,12325,12398,12470,12514,12561,12607,12656,12717,12778,12839,12901,12965,13029,13093,13158,13221,13281,13342,13408,13467,13527,13589,13660,13720,14419,14505,14755,14845,14932,15020,15102,15185,15275,17212,17264,17322,17367,17433,17497,17554,17611,19788,19845,19893,19942,20197,20567,20614,20872,22157,22503,22567,22689,23010,23084,23154,23232,23286,23356,23441,23489,23535,23596,23659,23725,23789,23860,23923,23988,24052,24113,24174,24226,24299,24373,24442,24517,24591,24665,24806,29037,29398,29476,29566,29654,29750,29840,30422,30511,30758,31039,31291,31576,31969,32446,32668,32890,33166,33393,33623,33853,34083,34313,34540,34959,35185,35610,35840,36268,36487,36770,36978,37109,37336,37762,37987,38414,38635,39060,39180,39456,39757,40081,40372,40686,40823,40954,41059,41301,41468,41672,41880,42151,42263,42375,42480,42597,42811,42957,43097,43183,43531,43619,43865,44283,44532,44614,44712,45369,45469,45721,46145,46400,46494,46583,46820,48844,49086,49188,49441,51597,62278,63794,74489,76017,77774,78400,78820,80081,81346,81602,81838,82385,82879,83484,83682,84262,85630,86005,86123,86661,86818,87014,87287,87543,87713,87854,87918,88283,88650,89326,89590,89928,90281,90375,90561,90867,91129,91254,91381,91620,91831,91950,92143,92320,92775,92956,93078,93337,93450,93637,93739,93846,93975,94250,94758,95254,96131,96425,96995,97144,97876,98048,98132,98468,98560,100626,105857,111228,111290,111868,112452,120399,120512,120741,120901,121053,121224,121390,121559,121726,121889,122132,122302,122475,122646,122920,123119,123324,123654,123738,123834,123930,124028,124128,124230,124332,124434,124536,124638,124738,124834,124946,125075,125198,125329,125460,125558,125672,125766,125906,126040,126136,126248,126348,126464,126560,126672,126772,126912,127048,127212,127342,127500,127650,127791,127935,128070,128182,128332,128460,128588,128724,128856,128986,129116,129228,130508,130654,130798,130936,131002,131092,131168,131272,131362,131464,131572,131680,131780,131860,131952,132050,132160,132212,132290,132396,132488,132592,132702,132824,132987,133144,133224,133324,133414,133524,133614,133855,133949,134055,134147,134247,134359,134473,134589,134705,134799,134913,135025,135127,135247,135369,135451,135555,135675,135801,135899,135993,136081,136193,136309,136431,136543,136718,136834,136920,137012,137124,137248,137315,137441,137509,137637,137781,137909,137978,138073,138188,138301,138400,138509,138620,138731,138832,138937,139037,139167,139258,139381,139475,139587,139673,139777,139873,139961,140079,140183,140287,140413,140501,140609,140709,140799,140909,140993,141095,141179,141233,141297,141403,141489,141599,141683,142087,144703,144821,144936,145016,145377,145963,147367,147445,148789,150150,150538,153381,163619,164358,166029,172842,177218,177969,178231,179078,179457,183735,184589,184818,189426,190436,192388,194788,198912,199656,201787,202127,203438", "endLines": "4,27,28,59,60,61,63,64,65,66,67,69,70,74,75,77,78,79,80,81,82,83,84,89,90,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,129,130,131,132,134,135,136,139,140,142,143,144,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,242,243,247,248,249,250,251,252,253,283,284,285,286,287,288,289,290,326,327,328,329,334,342,343,348,372,379,380,382,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,457,462,463,464,465,466,474,475,479,483,487,492,498,505,509,513,518,522,526,530,534,538,542,548,552,558,562,568,572,577,581,584,588,594,598,604,608,614,617,621,625,629,633,637,638,639,640,643,646,649,652,656,657,658,659,660,663,665,667,669,674,675,679,685,689,690,692,704,705,709,715,719,720,721,725,752,756,757,761,789,961,987,1158,1184,1215,1223,1229,1245,1267,1272,1277,1287,1296,1305,1309,1316,1335,1342,1343,1352,1355,1358,1362,1366,1370,1373,1374,1379,1384,1394,1399,1406,1412,1413,1416,1420,1425,1427,1429,1432,1435,1437,1441,1444,1451,1454,1457,1461,1463,1467,1469,1471,1473,1477,1485,1493,1505,1511,1520,1523,1534,1537,1538,1543,1544,1549,1642,1712,1713,1723,1732,1733,1886,1890,1893,1896,1899,1902,1905,1908,1911,1915,1918,1921,1924,1928,1931,1935,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1961,1963,1964,1965,1966,1967,1968,1969,1970,1972,1973,1975,1976,1978,1980,1981,1983,1984,1985,1986,1987,1988,1990,1991,1992,1993,1994,1995,2013,2015,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2031,2032,2033,2034,2035,2036,2037,2039,2043,2047,2048,2049,2050,2051,2052,2056,2057,2058,2059,2061,2063,2065,2067,2069,2070,2071,2072,2074,2076,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2092,2093,2094,2095,2097,2099,2100,2102,2103,2105,2107,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2122,2123,2124,2125,2127,2128,2129,2130,2131,2133,2135,2137,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2234,2237,2240,2243,2257,2263,2284,2319,2348,2375,2384,2448,2811,2820,2865,2903,3031,3163,3169,3175,3219,3343,3363,3377,3381,3527,3582,3626,3751,3805,3860,3872,3898,3905", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2136,2273,2343,2404,2479,2555,2679,2757,3052,3134,3261,3337,3414,3492,3598,3704,3783,3863,4169,4227,5103,5178,5243,5309,5369,5430,5502,5575,5642,5710,5769,5828,5887,5946,6005,6059,6113,6166,6220,6274,6328,6382,6847,6926,6999,7073,7216,7288,7361,7522,7580,7727,7801,7876,7999,8072,8142,8213,8273,8334,8403,8472,8542,8616,8692,8756,8833,8909,8986,9051,9120,9197,9272,9341,9409,9486,9552,9613,9710,9775,9844,9943,10014,10073,10131,10188,10247,10311,10382,10454,10526,10598,10670,10737,10805,10873,10932,10995,11059,11149,11240,11300,11366,11433,11499,11569,11633,11686,11753,11814,11881,11994,12052,12115,12180,12245,12320,12393,12465,12509,12556,12602,12651,12712,12773,12834,12896,12960,13024,13088,13153,13216,13276,13337,13403,13462,13522,13584,13655,13715,13783,14500,14587,14840,14927,15015,15097,15180,15270,15361,17259,17317,17362,17428,17492,17549,17606,17660,19840,19888,19937,19988,20226,20609,20658,20913,22184,22562,22624,22741,23079,23149,23227,23281,23351,23436,23484,23530,23591,23654,23720,23784,23855,23918,23983,24047,24108,24169,24221,24294,24368,24437,24512,24586,24660,24801,24871,29085,29471,29561,29649,29745,29835,30417,30506,30753,31034,31286,31571,31964,32441,32663,32885,33161,33388,33618,33848,34078,34308,34535,34954,35180,35605,35835,36263,36482,36765,36973,37104,37331,37757,37982,38409,38630,39055,39175,39451,39752,40076,40367,40681,40818,40949,41054,41296,41463,41667,41875,42146,42258,42370,42475,42592,42806,42952,43092,43178,43526,43614,43860,44278,44527,44609,44707,45364,45464,45716,46140,46395,46489,46578,46815,48839,49081,49183,49436,51592,62273,63789,74484,76012,77769,78395,78815,80076,81341,81597,81833,82380,82874,83479,83677,84257,85625,86000,86118,86656,86813,87009,87282,87538,87708,87849,87913,88278,88645,89321,89585,89923,90276,90370,90556,90862,91124,91249,91376,91615,91826,91945,92138,92315,92770,92951,93073,93332,93445,93632,93734,93841,93970,94245,94753,95249,96126,96420,96990,97139,97871,98043,98127,98463,98555,98833,105852,111223,111285,111863,112447,112538,120507,120736,120896,121048,121219,121385,121554,121721,121884,122127,122297,122470,122641,122915,123114,123319,123649,123733,123829,123925,124023,124123,124225,124327,124429,124531,124633,124733,124829,124941,125070,125193,125324,125455,125553,125667,125761,125901,126035,126131,126243,126343,126459,126555,126667,126767,126907,127043,127207,127337,127495,127645,127786,127930,128065,128177,128327,128455,128583,128719,128851,128981,129111,129223,129363,130649,130793,130931,130997,131087,131163,131267,131357,131459,131567,131675,131775,131855,131947,132045,132155,132207,132285,132391,132483,132587,132697,132819,132982,133139,133219,133319,133409,133519,133609,133850,133944,134050,134142,134242,134354,134468,134584,134700,134794,134908,135020,135122,135242,135364,135446,135550,135670,135796,135894,135988,136076,136188,136304,136426,136538,136713,136829,136915,137007,137119,137243,137310,137436,137504,137632,137776,137904,137973,138068,138183,138296,138395,138504,138615,138726,138827,138932,139032,139162,139253,139376,139470,139582,139668,139772,139868,139956,140074,140178,140282,140408,140496,140604,140704,140794,140904,140988,141090,141174,141228,141292,141398,141484,141594,141678,141798,144698,144816,144931,145011,145372,145605,146475,147440,148784,150145,150533,153376,163429,163749,165723,167381,173409,177964,178226,178426,179452,183730,184336,184813,184964,189636,191514,192695,197809,199651,201782,202122,203433,203636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\45796f7cc614cad85c7f924f55a8a968\\transformed\\jetified-lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "374", "startColumns": "4", "startOffsets": "22232", "endColumns": "42", "endOffsets": "22270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5d7ba366220000ba84259f9c0c265765\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2157,2904,2910", "startColumns": "4,4,4,4", "startOffsets": "164,141942,167386,167597", "endLines": "3,2159,2909,2993", "endColumns": "60,12,24,24", "endOffsets": "220,142082,167592,172108"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b9c7b9c889b6776307bff65fe344cf3\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "126,133,141,279,280,281,282,381,2001,2003,2004,2009,2011", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6572,7078,7585,17000,17053,17106,17159,22629,129686,129862,129984,130246,130441", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "6656,7144,7653,17048,17101,17154,17207,22684,129747,129979,130040,130307,130503"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\62392ea8da6b8d6d5858bdd34262c18b\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "344,375", "startColumns": "4,4", "startOffsets": "20663,22275", "endColumns": "41,59", "endOffsets": "20700,22330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9681fc2a5393925a96d5e781a21aacc5\\transformed\\jetified-core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "373", "startColumns": "4", "startOffsets": "22189", "endColumns": "42", "endOffsets": "22227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f35be5762b9bd4ab0c6bb255f6ea5c4a\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "62,127,272,273,274,275,276,277,278,339,340,341,384,385,440,442,452,453,459,460,461,1550,1734,1737,1743,1749,1752,1758,1762,1765,1772,1778,1781,1787,1792,1797,1804,1806,1812,1818,1826,1831,1838,1843,1849,1853,1860,1864,1870,1876,1879,1883,1884,2812,2827,2994,3032,3176,3364,3382,3446,3456,3466,3473,3479,3583,3752,3769", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2141,6661,16567,16631,16686,16754,16821,16886,16943,20410,20458,20506,22814,22877,27803,27909,28782,28826,29161,29300,29350,98838,112543,112648,112893,113231,113377,113717,113929,114092,114499,114837,114960,115299,115538,115795,116166,116226,116564,116850,117299,117591,117979,118284,118628,118873,119203,119410,119678,119951,120095,120296,120343,163434,163957,172113,173414,178431,184341,184969,186894,187176,187481,187743,188003,191519,197814,198344", "endLines": "62,127,272,273,274,275,276,277,278,339,340,341,384,385,440,442,452,455,459,460,461,1566,1736,1742,1748,1751,1757,1761,1764,1771,1777,1780,1786,1791,1796,1803,1805,1811,1817,1825,1830,1837,1842,1848,1852,1859,1863,1869,1875,1878,1882,1883,1884,2816,2837,3013,3035,3185,3371,3445,3455,3465,3472,3478,3521,3595,3768,3785", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2209,6725,16626,16681,16749,16816,16881,16938,16995,20453,20501,20562,22872,22935,27836,27961,28821,28961,29295,29345,29393,100271,112643,112888,113226,113372,113712,113924,114087,114494,114832,114955,115294,115533,115790,116161,116221,116559,116845,117294,117586,117974,118279,118623,118868,119198,119405,119673,119946,120090,120291,120338,120394,163614,164353,172837,173558,178758,184584,186889,187171,187476,187738,187998,189421,191966,198339,198907"}}, {"source": "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\android\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "5,12,11,4,3,15,16,8", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "202,430,378,148,99,526,578,288", "endColumns": "46,49,50,52,47,50,52,50", "endOffsets": "244,475,424,196,142,572,626,334"}, "to": {"startLines": "68,73,76,124,128,137,138,145", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2560,2922,3139,6456,6730,7366,7417,7881", "endColumns": "46,49,50,52,47,50,52,50", "endOffsets": "2602,2967,3185,6504,6773,7412,7465,7927"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bfa26447f798317d7f09bc1dbacdadf8\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "93,94,95,96,97,98,99,100,422,423,424,425,426,427,428,429,431,432,433,434,435,436,437,438,439,3186,3596", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4363,4453,4533,4623,4713,4793,4874,4954,25470,25575,25756,25881,25988,26168,26291,26407,26677,26865,26970,27151,27276,27451,27599,27662,27724,178763,191971", "endLines": "93,94,95,96,97,98,99,100,422,423,424,425,426,427,428,429,431,432,433,434,435,436,437,438,439,3198,3614", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4448,4528,4618,4708,4788,4869,4949,5029,25570,25751,25876,25983,26163,26286,26402,26505,26860,26965,27146,27271,27446,27594,27657,27719,27798,179073,192383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d0fda4cc076273a602c8713a382688ca\\transformed\\jetified-play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "383,430", "startColumns": "4,4", "startOffsets": "22746,26510", "endColumns": "67,166", "endOffsets": "22809,26672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bf2272c3db1a9b44d31b62c94be6b58\\transformed\\jetified-firebase-messaging-24.1.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "446", "startColumns": "4", "startOffsets": "28181", "endColumns": "81", "endOffsets": "28258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d4ef64409ea92c94e8ef2a7ab6d45e67\\transformed\\jetified-appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2285,2301,2307,3666,3682", "startColumns": "4,4,4,4,4", "startOffsets": "146480,146905,147083,194250,194661", "endLines": "2300,2306,2316,3681,3685", "endColumns": "24,24,24,24,24", "endOffsets": "146900,147078,147362,194656,194783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aee464e0380e5bd49865b4cb7d16db0c\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "376", "startColumns": "4", "startOffsets": "22335", "endColumns": "53", "endOffsets": "22384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca524d64babdcde41566288633a69bb5\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "414", "startColumns": "4", "startOffsets": "24876", "endColumns": "82", "endOffsets": "24954"}}]}]}