{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "D:/DALTI-APP-DEV/dalti-provider-flutter/dalti-customer/build/.cxx/debug/6g5w1tv6/armeabi-v7a", "source": "C:/src/flutter/packages/flutter_tools/gradle/src/main/scripts"}, "version": {"major": 2, "minor": 3}}