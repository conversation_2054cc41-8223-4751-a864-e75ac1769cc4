# Simple and reliable PowerShell script to remove print statements
param(
    [string]$LibPath = "lib",
    [switch]$DryRun = $false
)

Write-Host "Starting print statement removal..."
Write-Host "Target directory: $LibPath"
Write-Host "Dry run mode: $DryRun"

$dartFiles = Get-ChildItem -Path $LibPath -Recurse -Filter "*.dart"
Write-Host "Found $($dartFiles.Count) Dart files"

$totalRemoved = 0
$filesProcessed = 0

foreach ($file in $dartFiles) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Count existing print statements
    $printMatches = [regex]::Matches($content, "print\s*\(")
    $printCount = $printMatches.Count
    
    if ($printCount -gt 0) {
        Write-Host "Processing: $($file.Name) - $printCount prints"
        
        # Remove print statements using regex
        # Handle single line prints
        $content = [regex]::Replace($content, "(?m)^\s*print\s*\([^;]*\);\s*$", "")
        
        # Handle multi-line prints (basic pattern)
        $content = [regex]::Replace($content, "(?ms)^\s*print\s*\(\s*\n.*?\n\s*\);\s*$", "")
        
        # Clean up extra empty lines
        $content = [regex]::Replace($content, "(?m)^\s*\n\s*\n\s*\n", "`n`n")
        $content = [regex]::Replace($content, "(?m)^\s*\n\s*\n\s*\n", "`n`n")
        
        if (-not $DryRun) {
            Set-Content -Path $file.FullName -Value $content -NoNewline
        }
        
        $totalRemoved += $printCount
        $filesProcessed++
    }
}

Write-Host "COMPLETED!"
Write-Host "Files processed: $filesProcessed"
Write-Host "Print statements removed: $totalRemoved"

if ($DryRun) {
    Write-Host "This was a dry run - no files were modified"
}
