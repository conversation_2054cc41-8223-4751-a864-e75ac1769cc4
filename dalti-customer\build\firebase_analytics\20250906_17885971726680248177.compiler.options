"-Xallow-no-source-files" "-classpath" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\build\\firebase_analytics\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\build\\firebase_core\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\bae1aa13d5300f31f7b4c3a4448459e9\\transformed\\jetified-flutter_embedding_debug-1.0.0-a8bfdfc394deaed5c57bd45a64ac4294dc976a72.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c9578337b6a863752a1990fcbb0d8a84\\transformed\\jetified-firebase-analytics-23.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\16fe3b5c61ce5240cca863292e8eae30\\transformed\\jetified-play-services-measurement-23.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0e05f6333b5057a682eb55c1b0c0320e\\transformed\\jetified-play-services-measurement-api-23.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d19b90f5ce5a258046cc80ae745f72f4\\transformed\\jetified-play-services-measurement-sdk-23.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\42a819769441c1efe597ceb95737a72d\\transformed\\jetified-play-services-measurement-impl-23.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\69fd251e98aaa71488f070f35f6dc8b8\\transformed\\jetified-play-services-ads-identifier-18.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3ffc9b307825294a1c5da7975dc0cc81\\transformed\\jetified-play-services-measurement-sdk-api-23.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3383007b2c8f2587293581213e47cd96\\transformed\\jetified-play-services-measurement-base-23.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ee75f35a27cb6e3a8b1567450fd79bf7\\transformed\\jetified-play-services-stats-17.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5a93ae36fd84c3079020f82c3697c3bc\\transformed\\jetified-firebase-installations-19.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6ae65c075529c2e8c66eb48cb2de6e75\\transformed\\jetified-firebase-installations-interop-17.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c3767fe9aa5463da0887a069b6fe50fc\\transformed\\jetified-play-services-base-18.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e216f0bc331a73e08baa463783ee77b3\\transformed\\jetified-firebase-common-22.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8690cb7e22d7b60c201877931c6fcc34\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b8f9c560571fa98c465339b5209efd09\\transformed\\legacy-support-core-utils-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\36fcb88fcdfeeaa808ea51253e45df3c\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28b6d44f90d9a8e1089d3e8b00ffb7e4\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f46f889da5b981aab53d1b95a18a37b\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\727a27af5ed25a03014b8ca462d8c71b\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\40fb6781889309457ce30af3ba50da09\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0188d02f5654340af4abc26da775cc80\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1580dc876553585fdfd6d505dea8f186\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\31cf02c3828c9f71450707612543ba76\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1795fc0febd446d602a057d6df63b388\\transformed\\jetified-kotlinx-coroutines-android-1.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66b2a9e42f631339ae26685a0accb636\\transformed\\jetified-ads-adservices-java-1.1.0-beta11-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a3f7bdb18abd98ff5b9e9908f00f3337\\transformed\\jetified-ads-adservices-1.1.0-beta11-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1ff585340870431a8f3fa8f5461d5730\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\059473e67e2a6425f97b050fa11122f9\\transformed\\jetified-kotlinx-coroutines-play-services-1.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2420882b6262a9930ccdb5d4cc1fb7c6\\transformed\\jetified-play-services-tasks-18.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2d9a0b2b38805713aec755c201b10b22\\transformed\\jetified-firebase-measurement-connector-19.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\95287b549d9359bf71c481716f7d4331\\transformed\\jetified-play-services-basement-18.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8148685807e3078e4c002c7c2924ed8f\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ae5a805c37ae4262f97f54603df9d3b1\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1b09dc7974c994fb201d55b2261329c9\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\54ea9ecf2c470c6f54a51f94b508f807\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dea08e659f78cfb62c1f9ac3d092660e\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\abbaa8155b6ee7267e40f73e7ad76cce\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c08960cd47d04ac43525ef60d98a85aa\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\01c56b226a81d069ec33a113aaa3000a\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\704300e66752fef4ab4ad8be63a72a92\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d65c526a68655138292a79c247ed6f6f\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0a87d5a9ebe5f9d64767ca3fc1d9cdb2\\transformed\\documentfile-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b47c63432228be653311c289218562f5\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\95df9e0afc6f5016eb44ba791cf5139a\\transformed\\print-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9133671182168afc8026950eee9ae8de\\transformed\\jetified-annotation-jvm-1.8.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\11fd5315d356ec854b9ae3a38c367a3d\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\84429314a9e57ddf63348b4d271c6bfa\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a34d2bd48edac06469bce778bae8679e\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ada53c3e1462d07df0bd58a29edc1bdc\\transformed\\jetified-guava-31.1-android.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\07266bfd2b039d7c877514dc8bf182c7\\transformed\\jetified-listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\94f6aa77aa07758a8d030c774faff2fb\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dff54297e63e457d6fa19bf4cd277d25\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dd7eac768adc81f8480acf60fb45089b\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fba7e570c0d9ec8a337c3a97dae3d9ae\\transformed\\jetified-failureaccess-1.0.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\197cc679fb0780221a1b733433671bd7\\transformed\\jetified-jsr305-3.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\78fcce249cf23a783e192609f3cccbcc\\transformed\\jetified-checker-qual-3.12.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6d55a7adf7ea48e5233741b66c22a835\\transformed\\jetified-error_prone_annotations-2.26.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\37da53b6b0f4547ca78b805225f97887\\transformed\\jetified-j2objc-annotations-1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\af597dfa84b4461eb09945ef896ad5ff\\transformed\\jetified-firebase-components-19.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2ec80b3d65297a3644ddc436567dfab8\\transformed\\jetified-firebase-annotations-17.0.0.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-34\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\build\\firebase_analytics\\tmp\\kotlin-classes\\debug" "-jvm-target" "17" "-module-name" "firebase_analytics_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\build\\firebase_analytics\\generated\\source\\buildConfig\\debug\\io\\flutter\\plugins\\firebase\\analytics\\BuildConfig.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_analytics-12.0.1\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\firebase\\analytics\\Constants.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_analytics-12.0.1\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\firebase\\analytics\\FlutterFirebaseAnalyticsPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_analytics-12.0.1\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\firebase\\analytics\\FlutterFirebaseAppRegistrar.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_analytics-12.0.1\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\firebase\\analytics\\GeneratedAndroidFirebaseAnalytics.g.kt"