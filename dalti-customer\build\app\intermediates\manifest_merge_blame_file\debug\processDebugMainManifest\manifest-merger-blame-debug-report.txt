1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="org.adscloud.dalti.customer"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:4:5-67
15-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:4:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:5:5-79
16-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:5:22-76
17    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- Location Services -->
17-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:6:5-76
17-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:6:22-73
18    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
18-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:9:5-79
18-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:9:22-76
19    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
19-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:10:5-81
19-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:10:22-78
20    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> <!-- Camera & Media -->
20-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:11:5-85
20-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:11:22-82
21    <uses-permission android:name="android.permission.CAMERA" /> <!-- Storage Permissions -->
21-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:14:5-65
21-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:14:22-62
22    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
22-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:17:5-80
22-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:17:22-77
23    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- Android 13+ Media Permissions -->
23-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:18:5-81
23-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:18:22-78
24    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
24-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:20:5-76
24-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:20:22-73
25    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> <!-- Notifications & Messaging -->
25-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:21:5-75
25-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:21:22-72
26    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
26-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:24:5-77
26-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:24:22-74
27    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
27-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:25:5-79
27-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:25:22-76
28    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
28-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:26:5-74
28-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:26:22-71
29    <uses-permission android:name="android.permission.WAKE_LOCK" />
29-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:27:5-68
29-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:27:22-65
30    <uses-permission android:name="android.permission.VIBRATE" />
30-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:28:5-66
30-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:28:22-63
31    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- Background Services -->
31-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:29:5-81
31-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:29:22-78
32    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
32-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:32:5-77
32-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:32:22-74
33    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" /> <!-- Phone State (for phone number validation) -->
33-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:33:5-86
33-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:33:22-83
34    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- Firebase Cloud Messaging -->
34-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:36:5-75
34-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:36:22-72
35    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
35-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:39:5-82
35-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:39:22-79
36    <!--
37 Required to query activities that can process text, see:
38         https://developer.android.com/training/package-visibility and
39         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
40
41         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
42    -->
43    <queries>
43-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:132:5-137:15
44        <intent>
44-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:133:9-136:18
45            <action android:name="android.intent.action.PROCESS_TEXT" />
45-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:134:13-72
45-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:134:21-70
46
47            <data android:mimeType="text/plain" />
47-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:135:13-50
47-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\AndroidManifest.xml:135:19-48
48        </intent>
49    </queries>
50
51    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
51-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e0bc5286f514ee89a5c44e5182336a4\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:25:5-79
51-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e0bc5286f514ee89a5c44e5182336a4\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:25:22-76
52    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
52-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e0bc5286f514ee89a5c44e5182336a4\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:26:5-88
52-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e0bc5286f514ee89a5c44e5182336a4\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:26:22-85
53    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
53-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e0bc5286f514ee89a5c44e5182336a4\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:27:5-82
53-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e0bc5286f514ee89a5c44e5182336a4\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:27:22-79
54    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
54-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c4371f639d0a83da7b319e476cdbb94\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:26:5-110
54-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c4371f639d0a83da7b319e476cdbb94\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:26:22-107
55
56    <permission
56-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5672b6e30b06127e0c3f67c9f39e57a\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
57        android:name="org.adscloud.dalti.customer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
57-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5672b6e30b06127e0c3f67c9f39e57a\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
58        android:protectionLevel="signature" />
58-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5672b6e30b06127e0c3f67c9f39e57a\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
59
60    <uses-permission android:name="org.adscloud.dalti.customer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
60-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5672b6e30b06127e0c3f67c9f39e57a\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
60-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5672b6e30b06127e0c3f67c9f39e57a\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
61
62    <application
63        android:name="android.app.Application"
64        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
64-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5672b6e30b06127e0c3f67c9f39e57a\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
65        android:debuggable="true"
66        android:extractNativeLibs="false"
67        android:icon="@mipmap/ic_launcher"
68        android:label="Dalti Customer"
69        android:networkSecurityConfig="@xml/network_security_config"
70        android:usesCleartextTraffic="true" >
71        <activity
72            android:name="org.adscloud.dalti.customer.MainActivity"
73            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
74            android:exported="true"
75            android:hardwareAccelerated="true"
76            android:launchMode="singleTop"
77            android:taskAffinity=""
78            android:theme="@style/LaunchTheme"
79            android:windowSoftInputMode="adjustResize" >
80
81            <!--
82                 Specifies an Android theme to apply to this Activity as soon as
83                 the Android process has started. This theme is visible to the user
84                 while the Flutter UI initializes. After that, this theme continues
85                 to determine the Window background behind the Flutter UI.
86            -->
87            <meta-data
88                android:name="io.flutter.embedding.android.NormalTheme"
89                android:resource="@style/NormalTheme" />
90
91            <intent-filter>
92                <action android:name="android.intent.action.MAIN" />
93
94                <category android:name="android.intent.category.LAUNCHER" />
95            </intent-filter>
96            <!-- Intent filter for handling notification clicks -->
97            <intent-filter>
98                <action android:name="FLUTTER_NOTIFICATION_CLICK" />
99
100                <category android:name="android.intent.category.DEFAULT" />
101            </intent-filter>
102        </activity>
103
104        <!-- Firebase Cloud Messaging Service -->
105        <service
106            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
107            android:exported="false" >
108            <intent-filter>
108-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:50:13-52:29
109                <action android:name="com.google.firebase.MESSAGING_EVENT" />
109-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:51:17-78
109-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:51:25-75
110            </intent-filter>
111        </service>
112
113        <!-- Local Notifications Service -->
114        <receiver
115            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver"
116            android:exported="false" />
117        <receiver
118            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
119            android:exported="false" >
120            <intent-filter>
121                <action android:name="android.intent.action.BOOT_COMPLETED" />
122                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
123                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
124                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
125            </intent-filter>
126        </receiver>
127        <!--
128             Don't delete the meta-data below.
129             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
130        -->
131        <meta-data
132            android:name="flutterEmbedding"
133            android:value="2" />
134
135        <!-- Firebase Configuration -->
136        <meta-data
137            android:name="com.google.firebase.messaging.default_notification_icon"
138            android:resource="@mipmap/ic_launcher" />
139        <meta-data
140            android:name="com.google.firebase.messaging.default_notification_color"
141            android:resource="@color/notification_color" />
142        <meta-data
143            android:name="com.google.firebase.messaging.default_notification_channel_id"
144            android:value="dalti_notifications" />
145
146        <!-- Location Services Configuration -->
147        <meta-data
148            android:name="com.google.android.geo.API_KEY"
149            android:value="YOUR_GOOGLE_MAPS_API_KEY" />
150
151        <!-- File Provider for sharing files -->
152        <provider
153            android:name="androidx.core.content.FileProvider"
154            android:authorities="org.adscloud.dalti.customer.fileprovider"
155            android:exported="false"
156            android:grantUriPermissions="true" >
157            <meta-data
157-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
158                android:name="android.support.FILE_PROVIDER_PATHS"
158-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
159                android:resource="@xml/file_paths" />
159-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
160        </provider>
161
162        <service
162-->[:firebase_analytics] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:19
163            android:name="com.google.firebase.components.ComponentDiscoveryService"
163-->[:firebase_analytics] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:18-89
164            android:directBootAware="true"
164-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\332745d27e38ec1773168a826c04b248\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:32:13-43
165            android:exported="false" >
165-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:56:13-37
166            <meta-data
166-->[:firebase_analytics] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:85
167                android:name="com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar"
167-->[:firebase_analytics] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-128
168                android:value="com.google.firebase.components.ComponentRegistrar" />
168-->[:firebase_analytics] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-82
169            <meta-data
169-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
170                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
170-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
171                android:value="com.google.firebase.components.ComponentRegistrar" />
171-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
172            <meta-data
172-->[:firebase_core] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
173                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
173-->[:firebase_core] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
174                android:value="com.google.firebase.components.ComponentRegistrar" />
174-->[:firebase_core] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
175            <meta-data
175-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:57:13-59:85
176                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
176-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:58:17-122
177                android:value="com.google.firebase.components.ComponentRegistrar" />
177-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:59:17-82
178            <meta-data
178-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:60:13-62:85
179                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
179-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:61:17-119
180                android:value="com.google.firebase.components.ComponentRegistrar" />
180-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:62:17-82
181            <meta-data
181-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e0bc5286f514ee89a5c44e5182336a4\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:33:13-35:85
182                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
182-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e0bc5286f514ee89a5c44e5182336a4\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:34:17-139
183                android:value="com.google.firebase.components.ComponentRegistrar" />
183-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e0bc5286f514ee89a5c44e5182336a4\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:35:17-82
184            <meta-data
184-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac78172316572fb28a996e3daeef926e\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:15:13-17:85
185                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
185-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac78172316572fb28a996e3daeef926e\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:16:17-130
186                android:value="com.google.firebase.components.ComponentRegistrar" />
186-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac78172316572fb28a996e3daeef926e\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:17:17-82
187            <meta-data
187-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac78172316572fb28a996e3daeef926e\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:18:13-20:85
188                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
188-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac78172316572fb28a996e3daeef926e\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:19:17-127
189                android:value="com.google.firebase.components.ComponentRegistrar" />
189-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac78172316572fb28a996e3daeef926e\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:20:17-82
190            <meta-data
190-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\332745d27e38ec1773168a826c04b248\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:35:13-37:85
191                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
191-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\332745d27e38ec1773168a826c04b248\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:36:17-109
192                android:value="com.google.firebase.components.ComponentRegistrar" />
192-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\332745d27e38ec1773168a826c04b248\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:37:17-82
193            <meta-data
193-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5df48fea37914b180ba93c717df10cc\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
194                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
194-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5df48fea37914b180ba93c717df10cc\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
195                android:value="com.google.firebase.components.ComponentRegistrar" />
195-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5df48fea37914b180ba93c717df10cc\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
196        </service>
197        <service
197-->[:geolocator_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
198            android:name="com.baseflow.geolocator.GeolocatorLocationService"
198-->[:geolocator_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
199            android:enabled="true"
199-->[:geolocator_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
200            android:exported="false"
200-->[:geolocator_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
201            android:foregroundServiceType="location" />
201-->[:geolocator_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
202
203        <provider
203-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
204            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
204-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
205            android:authorities="org.adscloud.dalti.customer.flutter.image_provider"
205-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
206            android:exported="false"
206-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
207            android:grantUriPermissions="true" >
207-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
208            <meta-data
208-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
209                android:name="android.support.FILE_PROVIDER_PATHS"
209-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
210                android:resource="@xml/flutter_image_picker_file_paths" />
210-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
211        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
212        <service
212-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
213            android:name="com.google.android.gms.metadata.ModuleDependencies"
213-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
214            android:enabled="false"
214-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
215            android:exported="false" >
215-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
216            <intent-filter>
216-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
217                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
217-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
217-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
218            </intent-filter>
219
220            <meta-data
220-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
221                android:name="photopicker_activity:0:required"
221-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
222                android:value="" />
222-->[:image_picker_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
223        </service>
224
225        <activity
225-->[:url_launcher_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
226            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
226-->[:url_launcher_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
227            android:exported="false"
227-->[:url_launcher_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
228            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
228-->[:url_launcher_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
229
230        <service
230-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
231            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
231-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
232            android:exported="false"
232-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
233            android:permission="android.permission.BIND_JOB_SERVICE" />
233-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
234
235        <receiver
235-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
236            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
236-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
237            android:exported="true"
237-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
238            android:permission="com.google.android.c2dm.permission.SEND" >
238-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
239            <intent-filter>
239-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
240                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
240-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
240-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
241            </intent-filter>
242        </receiver>
243
244        <provider
244-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
245            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
245-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
246            android:authorities="org.adscloud.dalti.customer.flutterfirebasemessaginginitprovider"
246-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
247            android:exported="false"
247-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
248            android:initOrder="99" />
248-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
249
250        <uses-library
250-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b627014e4a89e473a30de52ef9e08c9\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
251            android:name="androidx.window.extensions"
251-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b627014e4a89e473a30de52ef9e08c9\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
252            android:required="false" />
252-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b627014e4a89e473a30de52ef9e08c9\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
253        <uses-library
253-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b627014e4a89e473a30de52ef9e08c9\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
254            android:name="androidx.window.sidecar"
254-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b627014e4a89e473a30de52ef9e08c9\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
255            android:required="false" />
255-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b627014e4a89e473a30de52ef9e08c9\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
256
257        <receiver
257-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:29:9-40:20
258            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
258-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:30:13-78
259            android:exported="true"
259-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:31:13-36
260            android:permission="com.google.android.c2dm.permission.SEND" >
260-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:32:13-73
261            <intent-filter>
261-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
262                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
262-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
262-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
263            </intent-filter>
264
265            <meta-data
265-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:37:13-39:40
266                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
266-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:38:17-92
267                android:value="true" />
267-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:39:17-37
268        </receiver>
269        <!--
270             FirebaseMessagingService performs security checks at runtime,
271             but set to not exported to explicitly avoid allowing another app to call it.
272        -->
273        <service
273-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:46:9-53:19
274            android:name="com.google.firebase.messaging.FirebaseMessagingService"
274-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:47:13-82
275            android:directBootAware="true"
275-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:48:13-43
276            android:exported="false" >
276-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:49:13-37
277            <intent-filter android:priority="-500" >
277-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:50:13-52:29
277-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:50:28-51
278                <action android:name="com.google.firebase.MESSAGING_EVENT" />
278-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:51:17-78
278-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\AndroidManifest.xml:51:25-75
279            </intent-filter>
280        </service>
281
282        <provider
282-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\332745d27e38ec1773168a826c04b248\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:23:9-28:39
283            android:name="com.google.firebase.provider.FirebaseInitProvider"
283-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\332745d27e38ec1773168a826c04b248\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:24:13-77
284            android:authorities="org.adscloud.dalti.customer.firebaseinitprovider"
284-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\332745d27e38ec1773168a826c04b248\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:25:13-72
285            android:directBootAware="true"
285-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\332745d27e38ec1773168a826c04b248\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:26:13-43
286            android:exported="false"
286-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\332745d27e38ec1773168a826c04b248\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:27:13-37
287            android:initOrder="100" />
287-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\332745d27e38ec1773168a826c04b248\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:28:13-36
288
289        <receiver
289-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c4371f639d0a83da7b319e476cdbb94\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:29:9-33:20
290            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
290-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c4371f639d0a83da7b319e476cdbb94\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:30:13-85
291            android:enabled="true"
291-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c4371f639d0a83da7b319e476cdbb94\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:31:13-35
292            android:exported="false" >
292-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c4371f639d0a83da7b319e476cdbb94\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:32:13-37
293        </receiver>
294
295        <service
295-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c4371f639d0a83da7b319e476cdbb94\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:35:9-38:40
296            android:name="com.google.android.gms.measurement.AppMeasurementService"
296-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c4371f639d0a83da7b319e476cdbb94\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:36:13-84
297            android:enabled="true"
297-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c4371f639d0a83da7b319e476cdbb94\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:37:13-35
298            android:exported="false" />
298-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c4371f639d0a83da7b319e476cdbb94\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:38:13-37
299        <service
299-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c4371f639d0a83da7b319e476cdbb94\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:39:9-43:72
300            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
300-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c4371f639d0a83da7b319e476cdbb94\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:40:13-87
301            android:enabled="true"
301-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c4371f639d0a83da7b319e476cdbb94\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:41:13-35
302            android:exported="false"
302-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c4371f639d0a83da7b319e476cdbb94\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:42:13-37
303            android:permission="android.permission.BIND_JOB_SERVICE" />
303-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c4371f639d0a83da7b319e476cdbb94\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:43:13-69
304
305        <provider
305-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd0f01ff653eb56f5facfc3f1b8cfba8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
306            android:name="androidx.startup.InitializationProvider"
306-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd0f01ff653eb56f5facfc3f1b8cfba8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
307            android:authorities="org.adscloud.dalti.customer.androidx-startup"
307-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd0f01ff653eb56f5facfc3f1b8cfba8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
308            android:exported="false" >
308-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd0f01ff653eb56f5facfc3f1b8cfba8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
309            <meta-data
309-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd0f01ff653eb56f5facfc3f1b8cfba8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
310                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
310-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd0f01ff653eb56f5facfc3f1b8cfba8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
311                android:value="androidx.startup" />
311-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd0f01ff653eb56f5facfc3f1b8cfba8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
312            <meta-data
312-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
313                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
313-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
314                android:value="androidx.startup" />
314-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
315        </provider>
316
317        <uses-library
317-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\8676ccfa02022d8c4fd8349174181d7f\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
318            android:name="android.ext.adservices"
318-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\8676ccfa02022d8c4fd8349174181d7f\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
319            android:required="false" />
319-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\8676ccfa02022d8c4fd8349174181d7f\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
320
321        <activity
321-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd831584ed04ca4fba5608ab323cf35f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
322            android:name="com.google.android.gms.common.api.GoogleApiActivity"
322-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd831584ed04ca4fba5608ab323cf35f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
323            android:exported="false"
323-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd831584ed04ca4fba5608ab323cf35f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
324            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
324-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd831584ed04ca4fba5608ab323cf35f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
325
326        <meta-data
326-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd004af28618635ed2c68077f32c9eb6\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
327            android:name="com.google.android.gms.version"
327-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd004af28618635ed2c68077f32c9eb6\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
328            android:value="@integer/google_play_services_version" />
328-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd004af28618635ed2c68077f32c9eb6\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
329
330        <receiver
330-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
331            android:name="androidx.profileinstaller.ProfileInstallReceiver"
331-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
332            android:directBootAware="false"
332-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
333            android:enabled="true"
333-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
334            android:exported="true"
334-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
335            android:permission="android.permission.DUMP" >
335-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
336            <intent-filter>
336-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
337                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
337-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
337-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
338            </intent-filter>
339            <intent-filter>
339-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
340                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
340-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
340-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
341            </intent-filter>
342            <intent-filter>
342-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
343                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
343-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
343-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
344            </intent-filter>
345            <intent-filter>
345-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
346                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
346-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
346-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
347            </intent-filter>
348        </receiver>
349
350        <service
350-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\29ccdb008888ae338d7affb2404de7b2\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
351            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
351-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\29ccdb008888ae338d7affb2404de7b2\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
352            android:exported="false" >
352-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\29ccdb008888ae338d7affb2404de7b2\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
353            <meta-data
353-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\29ccdb008888ae338d7affb2404de7b2\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
354                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
354-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\29ccdb008888ae338d7affb2404de7b2\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
355                android:value="cct" />
355-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\29ccdb008888ae338d7affb2404de7b2\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
356        </service>
357        <service
357-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\95223f0c2a649e46bfea8cfafdef43f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
358            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
358-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\95223f0c2a649e46bfea8cfafdef43f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
359            android:exported="false"
359-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\95223f0c2a649e46bfea8cfafdef43f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
360            android:permission="android.permission.BIND_JOB_SERVICE" >
360-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\95223f0c2a649e46bfea8cfafdef43f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
361        </service>
362
363        <receiver
363-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\95223f0c2a649e46bfea8cfafdef43f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
364            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
364-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\95223f0c2a649e46bfea8cfafdef43f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
365            android:exported="false" />
365-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\95223f0c2a649e46bfea8cfafdef43f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
366    </application>
367
368</manifest>
