# Simple PowerShell script to test FCM using Firebase CLI
Write-Host "🔥 FCM TESTING WITH FIREBASE CLI" -ForegroundColor Yellow
Write-Host "=================================" -ForegroundColor Yellow
Write-Host ""

# Configuration
$PROJECT_ID = "dalti-prod"
$FCM_TOKEN = "dX8_yF2uRyyVvcN-AmfjFl:APA91bFuCLThVntBVy-OHA_w12zbh1T7XYXcdV5nAKlxC1VRrFYfrMfHc5C3NjnhBIVW2ccv-QIBumqGpX5yMGQcgtuRNXp9RY3dJjK5mWGWKLYH9DtAuIM"

Write-Host "📋 Configuration:" -ForegroundColor Cyan
Write-Host "   Project ID: $PROJECT_ID" -ForegroundColor White
Write-Host "   FCM Token: $($FCM_TOKEN.Substring(0, 30))..." -ForegroundColor White
Write-Host ""

# Step 1: Get access token
Write-Host "1️⃣  Getting Firebase access token..." -ForegroundColor Green

try {
    $accessToken = npx -y firebase-tools@latest auth:print-access-token 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        $accessToken = $accessToken.Trim()
        Write-Host "✅ Access token obtained: $($accessToken.Substring(0, 20))..." -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to get access token" -ForegroundColor Red
        Write-Host "Error: $accessToken" -ForegroundColor Red
        Write-Host "Try: npx -y firebase-tools@latest login" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Step 2: Prepare payload
Write-Host "2️⃣  Preparing notification payload..." -ForegroundColor Green

$payload = @{
    message = @{
        token = $FCM_TOKEN
        notification = @{
            title = "🚀 Firebase CLI Test"
            body = "Testing FCM delivery via Firebase CLI"
        }
        data = @{
            test_type = "firebase_cli"
            timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        }
        android = @{
            priority = "high"
            notification = @{
                channel_id = "dalti_provider_notifications"
                icon = "launcher_icon"
                color = "#15424E"
            }
        }
    }
} | ConvertTo-Json -Depth 5

Write-Host "✅ Payload prepared" -ForegroundColor Green
Write-Host ""

# Step 3: Send notification
Write-Host "3️⃣  Sending FCM notification..." -ForegroundColor Green

$uri = "https://fcm.googleapis.com/v1/projects/$PROJECT_ID/messages:send"
$headers = @{
    "Authorization" = "Bearer $accessToken"
    "Content-Type" = "application/json"
}

Write-Host "🔗 Endpoint: $uri" -ForegroundColor Cyan
Write-Host "📤 Sending request..." -ForegroundColor Yellow
Write-Host ""

try {
    $response = Invoke-RestMethod -Uri $uri -Method POST -Headers $headers -Body $payload -ErrorAction Stop
    
    Write-Host "🎉 SUCCESS! Notification sent!" -ForegroundColor Green
    Write-Host "📱 Check your Android device now!" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "📋 Response:" -ForegroundColor Cyan
    Write-Host ($response | ConvertTo-Json -Depth 3) -ForegroundColor White
    
} catch {
    Write-Host "❌ FAILED to send notification" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "📊 HTTP Status: $statusCode" -ForegroundColor Red
        
        try {
            $errorStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorStream)
            $errorBody = $reader.ReadToEnd()
            $reader.Close()
            
            Write-Host "📥 Error Response:" -ForegroundColor Red
            Write-Host $errorBody -ForegroundColor White
            
        } catch {
            Write-Host "Could not read error response" -ForegroundColor Red
        }
    } else {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "📊 SUMMARY" -ForegroundColor Yellow
Write-Host "==========" -ForegroundColor Yellow

if ($response) {
    Write-Host "✅ FCM API call succeeded" -ForegroundColor Green
    Write-Host "🔍 If no notification appeared, check device settings" -ForegroundColor Yellow
} else {
    Write-Host "❌ FCM API call failed" -ForegroundColor Red
    Write-Host "🔧 Fix authentication/configuration issues" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎯 Next: Check Flutter console logs for message reception" -ForegroundColor Cyan
