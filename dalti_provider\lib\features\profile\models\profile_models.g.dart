// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'profile_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProfileCategoryImpl _$$ProfileCategoryImplFromJson(
        Map<String, dynamic> json) =>
    _$ProfileCategoryImpl(
      id: (json['id'] as num).toInt(),
      title: json['title'] as String,
    );

Map<String, dynamic> _$$ProfileCategoryImplToJson(
        _$ProfileCategoryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
    };

_$ProfileDataImpl _$$ProfileDataImplFromJson(Map<String, dynamic> json) =>
    _$ProfileDataImpl(
      id: (json['id'] as num).toInt(),
      userId: json['userId'] as String,
      title: json['title'] as String?,
      phone: json['phone'] as String?,
      presentation: json['presentation'] as String?,
      isVerified: json['isVerified'] as bool,
      isSetupComplete: json['isSetupComplete'] as bool,
      category: json['category'] == null
          ? null
          : ProfileCategory.fromJson(json['category'] as Map<String, dynamic>),
      averageRating: (json['averageRating'] as num?)?.toDouble(),
      totalReviews: (json['totalReviews'] as num).toInt(),
      profilePictureUrl: json['profilePictureUrl'] as String?,
      logoUrl: json['logoUrl'] as String?,
    );

Map<String, dynamic> _$$ProfileDataImplToJson(_$ProfileDataImpl instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'userId': instance.userId,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('title', instance.title);
  writeNotNull('phone', instance.phone);
  writeNotNull('presentation', instance.presentation);
  val['isVerified'] = instance.isVerified;
  val['isSetupComplete'] = instance.isSetupComplete;
  writeNotNull('category', instance.category?.toJson());
  writeNotNull('averageRating', instance.averageRating);
  val['totalReviews'] = instance.totalReviews;
  writeNotNull('profilePictureUrl', instance.profilePictureUrl);
  writeNotNull('logoUrl', instance.logoUrl);
  return val;
}

_$ProfileUpdateRequestImpl _$$ProfileUpdateRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$ProfileUpdateRequestImpl(
      title: json['title'] as String?,
      phone: json['phone'] as String?,
      presentation: json['presentation'] as String?,
      providerCategoryId: (json['providerCategoryId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ProfileUpdateRequestImplToJson(
    _$ProfileUpdateRequestImpl instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('title', instance.title);
  writeNotNull('phone', instance.phone);
  writeNotNull('presentation', instance.presentation);
  writeNotNull('providerCategoryId', instance.providerCategoryId);
  return val;
}

_$ProfileResponseImpl _$$ProfileResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ProfileResponseImpl(
      success: json['success'] as bool,
      data: json['data'] == null
          ? null
          : ProfileData.fromJson(json['data'] as Map<String, dynamic>),
      message: json['message'] as String?,
    );

Map<String, dynamic> _$$ProfileResponseImplToJson(
    _$ProfileResponseImpl instance) {
  final val = <String, dynamic>{
    'success': instance.success,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('data', instance.data?.toJson());
  writeNotNull('message', instance.message);
  return val;
}

_$ProfileErrorImpl _$$ProfileErrorImplFromJson(Map<String, dynamic> json) =>
    _$ProfileErrorImpl(
      code: json['code'] as String,
      message: json['message'] as String,
      details: json['details'] as String?,
      validationErrors: (json['validationErrors'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$ProfileErrorImplToJson(_$ProfileErrorImpl instance) {
  final val = <String, dynamic>{
    'code': instance.code,
    'message': instance.message,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('details', instance.details);
  writeNotNull('validationErrors', instance.validationErrors);
  return val;
}

_$ProfilePictureUploadResponseImpl _$$ProfilePictureUploadResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ProfilePictureUploadResponseImpl(
      uploadUrl: json['uploadUrl'] as String,
      uploadFields: json['uploadFields'] as Map<String, dynamic>,
      file: UploadFile.fromJson(json['file'] as Map<String, dynamic>),
      user: UploadUser.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ProfilePictureUploadResponseImplToJson(
        _$ProfilePictureUploadResponseImpl instance) =>
    <String, dynamic>{
      'uploadUrl': instance.uploadUrl,
      'uploadFields': instance.uploadFields,
      'file': instance.file.toJson(),
      'user': instance.user.toJson(),
    };

_$UploadFileImpl _$$UploadFileImplFromJson(Map<String, dynamic> json) =>
    _$UploadFileImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      key: json['key'] as String,
    );

Map<String, dynamic> _$$UploadFileImplToJson(_$UploadFileImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'key': instance.key,
    };

_$UploadUserImpl _$$UploadUserImplFromJson(Map<String, dynamic> json) =>
    _$UploadUserImpl(
      id: json['id'] as String,
      profilePictureId: json['profilePictureId'] as String,
    );

Map<String, dynamic> _$$UploadUserImplToJson(_$UploadUserImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'profilePictureId': instance.profilePictureId,
    };

_$ProfilePictureDetailsImpl _$$ProfilePictureDetailsImplFromJson(
        Map<String, dynamic> json) =>
    _$ProfilePictureDetailsImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      key: json['key'] as String,
      downloadUrl: json['downloadUrl'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$$ProfilePictureDetailsImplToJson(
        _$ProfilePictureDetailsImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'key': instance.key,
      'downloadUrl': instance.downloadUrl,
      'createdAt': instance.createdAt.toIso8601String(),
    };

_$ProfilePictureInfoImpl _$$ProfilePictureInfoImplFromJson(
        Map<String, dynamic> json) =>
    _$ProfilePictureInfoImpl(
      hasProfilePicture: json['hasProfilePicture'] as bool,
      profilePicture: json['profilePicture'] == null
          ? null
          : ProfilePictureDetails.fromJson(
              json['profilePicture'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ProfilePictureInfoImplToJson(
    _$ProfilePictureInfoImpl instance) {
  final val = <String, dynamic>{
    'hasProfilePicture': instance.hasProfilePicture,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('profilePicture', instance.profilePicture?.toJson());
  return val;
}

_$LogoDetailsImpl _$$LogoDetailsImplFromJson(Map<String, dynamic> json) =>
    _$LogoDetailsImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      key: json['key'] as String,
      downloadUrl: json['downloadUrl'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$$LogoDetailsImplToJson(_$LogoDetailsImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'key': instance.key,
      'downloadUrl': instance.downloadUrl,
      'createdAt': instance.createdAt.toIso8601String(),
    };

_$LogoInfoImpl _$$LogoInfoImplFromJson(Map<String, dynamic> json) =>
    _$LogoInfoImpl(
      hasLogo: json['hasLogo'] as bool,
      logo: json['logo'] == null
          ? null
          : LogoDetails.fromJson(json['logo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$LogoInfoImplToJson(_$LogoInfoImpl instance) {
  final val = <String, dynamic>{
    'hasLogo': instance.hasLogo,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('logo', instance.logo?.toJson());
  return val;
}
