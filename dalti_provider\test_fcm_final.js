#!/usr/bin/env node

/**
 * Simple FCM Test using Firebase CLI access token
 */

const { exec } = require('child_process');
const https = require('https');

console.log('🔥 FCM TESTING WITH FIREBASE CLI');
console.log('=================================\n');

const PROJECT_ID = 'dalti-prod';
const FCM_TOKEN = 'dX8_yF2uRyyVvcN-AmfjFl:APA91bFuCLThVntBVy-OHA_w12zbh1T7XYXcdV5nAKlxC1VRrFYfrMfHc5C3NjnhBIVW2ccv-QIBumqGpX5yMGQcgtuRNXp9RY3dJjK5mWGWKLYH9DtAuIM';

console.log(`📋 Project ID: ${PROJECT_ID}`);
console.log(`📱 FCM Token: ${FCM_TOKEN.substring(0, 30)}...\n`);

// Step 1: Get access token from Firebase CLI
function getAccessToken() {
    return new Promise((resolve, reject) => {
        console.log('1️⃣  Getting Firebase access token...');
        
        exec('npx -y firebase-tools@latest auth:print-access-token', (error, stdout, stderr) => {
            if (error) {
                console.log('❌ Failed to get access token');
                console.log('Error:', error.message);
                console.log('Try: npx -y firebase-tools@latest login');
                reject(error);
                return;
            }
            
            const accessToken = stdout.trim();
            console.log(`✅ Access token obtained: ${accessToken.substring(0, 20)}...\n`);
            resolve(accessToken);
        });
    });
}

// Step 2: Send FCM notification
function sendNotification(accessToken) {
    return new Promise((resolve) => {
        console.log('2️⃣  Sending FCM notification...');
        
        const payload = {
            message: {
                token: FCM_TOKEN,
                notification: {
                    title: "🚀 Firebase CLI Test",
                    body: "Testing FCM delivery via Firebase CLI access token"
                },
                data: {
                    test_type: "firebase_cli",
                    timestamp: new Date().toISOString(),
                    source: "node_script"
                },
                android: {
                    priority: "high",
                    notification: {
                        channel_id: "dalti_provider_notifications",
                        icon: "launcher_icon",
                        color: "#15424E",
                        sound: "default"
                    }
                }
            }
        };
        
        const postData = JSON.stringify(payload);
        
        const options = {
            hostname: 'fcm.googleapis.com',
            port: 443,
            path: `/v1/projects/${PROJECT_ID}/messages:send`,
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };
        
        console.log(`🔗 Endpoint: https://fcm.googleapis.com/v1/projects/${PROJECT_ID}/messages:send`);
        console.log(`🔑 Authorization: Bearer ${accessToken.substring(0, 20)}...`);
        console.log('📤 Sending request...\n');
        
        const req = https.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                console.log(`📊 HTTP Status: ${res.statusCode}`);
                console.log(`📥 Response: ${responseData}\n`);
                
                if (res.statusCode === 200) {
                    console.log('🎉 SUCCESS! Notification sent successfully!');
                    console.log('📱 Check your Android device now - you should see the notification!');
                    
                    try {
                        const response = JSON.parse(responseData);
                        console.log(`📋 Message ID: ${response.name}`);
                    } catch (e) {
                        // Response parsing failed, but request succeeded
                    }
                } else {
                    console.log('❌ FAILED to send notification');
                    
                    try {
                        const error = JSON.parse(responseData);
                        console.log(`💥 Error Code: ${error.error.code}`);
                        console.log(`💥 Error Message: ${error.error.message}`);
                        console.log(`💥 Error Status: ${error.error.status}`);
                        
                        // Provide specific guidance
                        switch (error.error.code) {
                            case 401:
                                console.log('\n💡 SOLUTION: Authentication issue');
                                console.log('   Try: npx -y firebase-tools@latest login');
                                break;
                            case 403:
                                console.log('\n💡 SOLUTION: Permission issue');
                                console.log(`   Check FCM permissions for project: ${PROJECT_ID}`);
                                break;
                            case 404:
                                console.log('\n💡 SOLUTION: Project not found');
                                console.log(`   Verify project ID: ${PROJECT_ID}`);
                                break;
                            case 400:
                                console.log('\n💡 SOLUTION: Invalid request');
                                console.log('   Check FCM token format and payload');
                                break;
                        }
                    } catch (e) {
                        console.log(`💥 Raw error: ${responseData}`);
                    }
                }
                
                resolve(res.statusCode === 200);
            });
        });
        
        req.on('error', (error) => {
            console.log(`❌ Request failed: ${error.message}`);
            resolve(false);
        });
        
        req.write(postData);
        req.end();
    });
}

// Main execution
async function main() {
    try {
        const accessToken = await getAccessToken();
        const success = await sendNotification(accessToken);
        
        console.log('\n📊 DEBUGGING SUMMARY');
        console.log('====================\n');
        
        if (success) {
            console.log('✅ FCM API call succeeded');
            console.log('✅ Authentication working');
            console.log('✅ Project configuration correct\n');
            
            console.log('🔍 If notification did NOT appear on device:');
            console.log('   1. Check device notification settings');
            console.log('   2. Verify app notification permissions');
            console.log('   3. Check Do Not Disturb mode');
            console.log('   4. Verify battery optimization settings');
            console.log('   5. Try with app in foreground vs background');
            console.log('   6. Check Flutter console logs for message reception\n');
            
            console.log('📱 DEVICE TESTING TIPS:');
            console.log('   • Keep Dalti Provider app open while testing');
            console.log('   • Watch both notification panel AND Flutter console');
            console.log('   • Test with app in foreground, then background');
        } else {
            console.log('❌ FCM API call failed');
            console.log('🔧 Fix authentication/configuration issues above');
        }
        
        console.log('\n🎯 NEXT STEPS:');
        console.log('1. If API succeeded: Issue is device-specific settings');
        console.log('2. If API failed: Issue is project/authentication related');
        console.log('3. Check Flutter console for FCM message reception logs');
        
    } catch (error) {
        console.log(`❌ Script failed: ${error.message}`);
        console.log('\n🔧 Make sure you are logged in to Firebase CLI:');
        console.log('   npx -y firebase-tools@latest login');
    }
}

main();
