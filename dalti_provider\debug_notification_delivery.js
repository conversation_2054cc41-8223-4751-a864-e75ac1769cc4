#!/usr/bin/env node

/**
 * Firebase Console Notification Delivery Debugger
 * Tests why notifications sent from Firebase Console aren't reaching the device
 */

const fs = require('fs');
const { exec } = require('child_process');

console.log('🔍 FIREBASE CONSOLE NOTIFICATION DELIVERY DEBUGGER');
console.log('===================================================\n');

// Your current FCM token from the logs
const CURRENT_TOKEN = 'dX8_yF2uRyyVvcN-AmfjFl:APA91bFuCLThVntBVy-OHA_w12zbh1T7XYXcdV5nAKlxC1VRrFYfrMfHc5C3NjnhBIVW2ccv-QIBumqGpX5yMGQcgtuRNXp9RY3dJjK5mWGWKLYH9DtAuIM';

let issues = [];
let checks = [];

function addCheck(status, message) {
    const emoji = status ? '✅' : '❌';
    const fullMessage = `${emoji} ${message}`;
    checks.push({ status, message: fullMessage });
    console.log(fullMessage);
    if (!status) issues.push(message);
}

function addInfo(message) {
    console.log(`ℹ️  ${message}`);
}

console.log('1️⃣  DEVICE & APP STATE ANALYSIS\n');

// Check if device is connected and app is running
function checkDeviceState() {
    return new Promise((resolve) => {
        exec('adb devices', (error, stdout, stderr) => {
            if (error) {
                addCheck(false, 'ADB not available - cannot check device state');
                resolve();
                return;
            }
            
            const devices = stdout.split('\n').filter(line => 
                line.includes('device') && !line.includes('List of devices')
            );
            
            if (devices.length > 0) {
                addCheck(true, `Device connected: ${devices[0].split('\t')[0]}`);
                
                // Check if app is running
                exec('adb shell "ps | grep dalti"', (error, stdout, stderr) => {
                    if (stdout.includes('dalti')) {
                        addCheck(true, 'Dalti app is running on device');
                    } else {
                        addCheck(false, 'Dalti app is NOT running on device');
                    }
                    resolve();
                });
            } else {
                addCheck(false, 'No Android device connected via ADB');
                resolve();
            }
        });
    });
}

// Check notification settings on device
function checkNotificationSettings() {
    return new Promise((resolve) => {
        exec('adb shell "dumpsys notification | grep dalti"', (error, stdout, stderr) => {
            if (error) {
                addCheck(false, 'Cannot check notification settings via ADB');
                resolve();
                return;
            }
            
            if (stdout.includes('dalti')) {
                addCheck(true, 'App has notification entries in system');
            } else {
                addCheck(false, 'App has NO notification entries in system');
            }
            resolve();
        });
    });
}

// Check Do Not Disturb and notification policies
function checkSystemNotificationState() {
    return new Promise((resolve) => {
        exec('adb shell "settings get global zen_mode"', (error, stdout, stderr) => {
            if (error) {
                addCheck(false, 'Cannot check Do Not Disturb state');
                resolve();
                return;
            }
            
            const zenMode = stdout.trim();
            if (zenMode === '0') {
                addCheck(true, 'Do Not Disturb is OFF');
            } else {
                addCheck(false, `Do Not Disturb is ON (mode: ${zenMode})`);
            }
            resolve();
        });
    });
}

console.log('\n2️⃣  FCM TOKEN VALIDATION\n');

// Validate token format
function validateTokenFormat() {
    addInfo(`Testing token: ${CURRENT_TOKEN.substring(0, 30)}...`);
    
    // Check token length (should be around 140-160 characters)
    if (CURRENT_TOKEN.length >= 140 && CURRENT_TOKEN.length <= 200) {
        addCheck(true, `Token length valid: ${CURRENT_TOKEN.length} characters`);
    } else {
        addCheck(false, `Token length invalid: ${CURRENT_TOKEN.length} characters`);
    }
    
    // Check token format (should contain colon)
    if (CURRENT_TOKEN.includes(':')) {
        addCheck(true, 'Token format valid (contains colon separator)');
    } else {
        addCheck(false, 'Token format invalid (missing colon separator)');
    }
    
    // Check token prefix
    if (CURRENT_TOKEN.startsWith('dX8_') || CURRENT_TOKEN.startsWith('c') || CURRENT_TOKEN.startsWith('f')) {
        addCheck(true, 'Token prefix appears valid');
    } else {
        addCheck(false, 'Token prefix appears invalid');
    }
}

console.log('\n3️⃣  FIREBASE PROJECT CONFIGURATION\n');

// Check Firebase project configuration
function checkFirebaseConfig() {
    const googleServicesPath = 'android/app/google-services.json';
    
    if (!fs.existsSync(googleServicesPath)) {
        addCheck(false, 'google-services.json not found');
        return;
    }
    
    try {
        const config = JSON.parse(fs.readFileSync(googleServicesPath, 'utf8'));
        
        addCheck(true, `Firebase project: ${config.project_info.project_id}`);
        
        // Check if project has FCM enabled
        if (config.client && config.client[0] && config.client[0].services) {
            const services = config.client[0].services;
            if (services.firebase_messaging) {
                addCheck(true, 'Firebase Messaging service enabled in project');
            } else {
                addCheck(false, 'Firebase Messaging service NOT enabled in project');
            }
        }
        
        // Check package name match
        const packageName = config.client[0].client_info.android_client_info.package_name;
        addInfo(`Package name in config: ${packageName}`);
        
        // Check if SHA certificates are configured
        if (config.client[0].oauth_client && config.client[0].oauth_client.length > 0) {
            addCheck(true, 'OAuth client configured (SHA certificates present)');
        } else {
            addCheck(false, 'No OAuth client configured (missing SHA certificates)');
        }
        
    } catch (e) {
        addCheck(false, 'Invalid google-services.json format');
    }
}

console.log('\n4️⃣  NOTIFICATION CHANNEL ANALYSIS\n');

// Check notification channel configuration
function checkNotificationChannels() {
    const manifestPath = 'android/app/src/main/AndroidManifest.xml';
    
    if (!fs.existsSync(manifestPath)) {
        addCheck(false, 'AndroidManifest.xml not found');
        return;
    }
    
    const manifest = fs.readFileSync(manifestPath, 'utf8');
    
    // Check default notification channel
    const channelMatch = manifest.match(/default_notification_channel_id[\s\S]*?value="([^"]+)"/);
    if (channelMatch) {
        const channelId = channelMatch[1];
        addCheck(true, `Default notification channel: ${channelId}`);
        
        // Check if service uses same channel
        const servicePath = 'lib/core/services/firebase_messaging_service.dart';
        if (fs.existsSync(servicePath)) {
            const service = fs.readFileSync(servicePath, 'utf8');
            if (service.includes(`'${channelId}'`)) {
                addCheck(true, 'Service uses matching notification channel');
            } else {
                addCheck(false, 'Service uses DIFFERENT notification channel');
            }
        }
    } else {
        addCheck(false, 'No default notification channel configured');
    }
}

console.log('\n5️⃣  LIVE DEVICE TESTING\n');

// Test notification reception capability
function testNotificationCapability() {
    return new Promise((resolve) => {
        // Test if device can receive any notifications at all
        exec('adb shell "am broadcast -a android.intent.action.BOOT_COMPLETED"', (error, stdout, stderr) => {
            if (error) {
                addCheck(false, 'Cannot send test broadcast to device');
            } else {
                addCheck(true, 'Device can receive system broadcasts');
            }
            
            // Check notification access
            exec('adb shell "dumpsys notification | grep -A 5 -B 5 enabled"', (error, stdout, stderr) => {
                if (stdout.includes('enabled=true')) {
                    addCheck(true, 'Notification system is enabled');
                } else {
                    addCheck(false, 'Notification system may be disabled');
                }
                resolve();
            });
        });
    });
}

// Generate comprehensive report
async function generateReport() {
    console.log('\n6️⃣  COMPREHENSIVE ANALYSIS\n');
    
    await checkDeviceState();
    await checkNotificationSettings();
    await checkSystemNotificationState();
    
    validateTokenFormat();
    checkFirebaseConfig();
    checkNotificationChannels();
    
    await testNotificationCapability();
    
    console.log('\n📊 DELIVERY DEBUGGING SUMMARY');
    console.log('==============================\n');
    
    const passedChecks = checks.filter(c => c.status).length;
    const failedChecks = checks.filter(c => !c.status).length;
    
    console.log(`✅ Passed: ${passedChecks}`);
    console.log(`❌ Failed: ${failedChecks}\n`);
    
    if (failedChecks === 0) {
        console.log('🎉 ALL CHECKS PASSED!');
        console.log('Configuration appears correct. If notifications still don\'t work:');
        console.log('1. Try restarting the app completely');
        console.log('2. Check device notification settings manually');
        console.log('3. Try sending notification when app is in foreground vs background');
        console.log('4. Check Firebase Console logs for delivery status');
    } else {
        console.log('🚨 ISSUES FOUND:');
        issues.forEach(issue => console.log(`   • ${issue}`));
        
        console.log('\n🔧 RECOMMENDED FIXES:');
        
        if (issues.some(i => i.includes('Do Not Disturb'))) {
            console.log('   • Turn off Do Not Disturb mode on device');
        }
        
        if (issues.some(i => i.includes('app is NOT running'))) {
            console.log('   • Make sure the Dalti app is running on device');
        }
        
        if (issues.some(i => i.includes('notification entries'))) {
            console.log('   • Check app notification permissions in device settings');
        }
        
        if (issues.some(i => i.includes('OAuth client'))) {
            console.log('   • Add SHA certificates to Firebase project');
        }
        
        if (issues.some(i => i.includes('DIFFERENT notification channel'))) {
            console.log('   • Fix notification channel ID mismatch in code');
        }
    }
    
    console.log('\n🧪 MANUAL TESTING STEPS:');
    console.log('1. Open Firebase Console > Cloud Messaging');
    console.log('2. Click "Send your first message"');
    console.log('3. Enter title: "Test Notification"');
    console.log('4. Enter text: "Testing FCM delivery"');
    console.log('5. Click "Send test message"');
    console.log('6. Paste this token:');
    console.log(`   ${CURRENT_TOKEN}`);
    console.log('7. Click "Test"');
    console.log('8. Check device for notification (try both foreground and background)');
    
    // Save detailed report
    const report = {
        timestamp: new Date().toISOString(),
        token: CURRENT_TOKEN,
        summary: { passed: passedChecks, failed: failedChecks },
        checks: checks,
        issues: issues
    };
    
    fs.writeFileSync('notification_delivery_debug.json', JSON.stringify(report, null, 2));
    console.log('\n📄 Detailed report saved to: notification_delivery_debug.json');
}

generateReport().catch(console.error);
