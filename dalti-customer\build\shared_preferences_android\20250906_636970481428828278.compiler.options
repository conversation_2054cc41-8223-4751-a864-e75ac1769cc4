"-Xallow-no-source-files" "-classpath" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\build\\shared_preferences_android\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\bae1aa13d5300f31f7b4c3a4448459e9\\transformed\\jetified-flutter_embedding_debug-1.0.0-a8bfdfc394deaed5c57bd45a64ac4294dc976a72.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\589a6365f7196e913aebd7f90f12fac2\\transformed\\preference-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3798fd13159115fb8f84095c327ccd1f\\transformed\\appcompat-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8deb86c181dcc45132c87eadb91a4b6a\\transformed\\jetified-fragment-ktx-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8148685807e3078e4c002c7c2924ed8f\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a8e4b78f9dcbe4aea0604e2121f9a07b\\transformed\\recyclerview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\bca8c99758c0bc0579de07d55885d676\\transformed\\jetified-activity-ktx-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8690cb7e22d7b60c201877931c6fcc34\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\52f3418ad1e58e0de6b09df00e11976e\\transformed\\legacy-support-core-ui-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b8f9c560571fa98c465339b5209efd09\\transformed\\legacy-support-core-utils-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\36fcb88fcdfeeaa808ea51253e45df3c\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f46f889da5b981aab53d1b95a18a37b\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28b6d44f90d9a8e1089d3e8b00ffb7e4\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9ab8b32a522db8a35f61cd550b150b71\\transformed\\jetified-lifecycle-viewmodel-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\471d5e4872c7a0b3e5cce33ca062ed65\\transformed\\jetified-lifecycle-runtime-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\727a27af5ed25a03014b8ca462d8c71b\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\40fb6781889309457ce30af3ba50da09\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0188d02f5654340af4abc26da775cc80\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ae5a805c37ae4262f97f54603df9d3b1\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1b09dc7974c994fb201d55b2261329c9\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d3c9604aaba71ee1c34c011bbb601885\\transformed\\jetified-appcompat-resources-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5e8c143be280105972f6711ce7d79de5\\transformed\\drawerlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\63537321f5dfb6f00fe1c6cd97921461\\transformed\\slidingpanelayout-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df85fde64b7c96287abe89945bbcbf68\\transformed\\coordinatorlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a43307913bd670f89e500d8b495a55e2\\transformed\\customview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8f3a0e9fad7e39a4dc66dd8b85f6dc58\\transformed\\vectordrawable-animated-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\954de027ed51ed6a64e53431f4089a2d\\transformed\\vectordrawable-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6ed710ccdca2f880d32f4ea44cbf4eb9\\transformed\\swiperefreshlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\edc44364d1346ed74d2e8fdb8bc6aee4\\transformed\\asynclayoutinflater-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dea08e659f78cfb62c1f9ac3d092660e\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\abbaa8155b6ee7267e40f73e7ad76cce\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c08960cd47d04ac43525ef60d98a85aa\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\31cf02c3828c9f71450707612543ba76\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1580dc876553585fdfd6d505dea8f186\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\62fb3cc3174d73ef036066c0584f46e6\\transformed\\jetified-datastore-preferences-core-jvm-1.1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\addbbb38e43e36cc222a0ae236c1422e\\transformed\\jetified-datastore-core-okio-jvm-1.1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\96f2ce18c2c67ef56170111f6a567937\\transformed\\jetified-datastore-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\08ff2e1636f13e1fe49ef47a6842a83d\\transformed\\jetified-datastore-preferences-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4259d417d84b0176017ea2b488fe582f\\transformed\\jetified-datastore-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\11fd5315d356ec854b9ae3a38c367a3d\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8e41480b6dfca2e657722d95a71b76f0\\transformed\\jetified-savedstate-ktx-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d65c526a68655138292a79c247ed6f6f\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\01c56b226a81d069ec33a113aaa3000a\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\704300e66752fef4ab4ad8be63a72a92\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c0a9732096418ce9788a015893e5d06b\\transformed\\jetified-collection-ktx-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c488240be6930a09d92107f21868ddf6\\transformed\\cursoradapter-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e1b2ef508b4d350d2bcda9b870da26f7\\transformed\\interpolator-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0a87d5a9ebe5f9d64767ca3fc1d9cdb2\\transformed\\documentfile-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b47c63432228be653311c289218562f5\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\95df9e0afc6f5016eb44ba791cf5139a\\transformed\\print-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9c13680ed37363ac734df3fbf62515ed\\transformed\\jetified-annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\86c2be6fb3a67233ebd99595cd5dafd7\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5893e7ccf2de2589af84cf8e7030df54\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cc14d69d2f08dd42725d6c632eaf71c5\\transformed\\jetified-okio-jvm-3.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0f24c7d1cf4cb2801215920a71eebb95\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4816e84b809ca9a2703239fda91bbb44\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\84429314a9e57ddf63348b4d271c6bfa\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a34d2bd48edac06469bce778bae8679e\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\94f6aa77aa07758a8d030c774faff2fb\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dff54297e63e457d6fa19bf4cd277d25\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dd7eac768adc81f8480acf60fb45089b\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-36\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\build\\shared_preferences_android\\tmp\\kotlin-classes\\debug" "-jvm-target" "11" "-module-name" "shared_preferences_android_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\LegacySharedPreferencesPlugin.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\Messages.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\SharedPreferencesListEncoder.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\MessagesAsync.g.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\SharedPreferencesPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\StringListObjectInputStream.kt"