// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$customerApiServiceHash() =>
    r'23a69009d6015d278fcc87d583fe3781e7386995';

/// Provider for customer API service
///
/// Copied from [customerApiService].
@ProviderFor(customerApiService)
final customerApiServiceProvider =
    AutoDisposeProvider<CustomerApiService>.internal(
  customerApiService,
  name: r'customerApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$customerApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CustomerApiServiceRef = AutoDisposeProviderRef<CustomerApiService>;
String _$customerRepositoryHash() =>
    r'4afbb9fab6880729c1ffe4965b082fe5a9889552';

/// Provider for customer repository
///
/// Copied from [customerRepository].
@ProviderFor(customerRepository)
final customerRepositoryProvider =
    AutoDisposeProvider<CustomerRepository>.internal(
  customerRepository,
  name: r'customerRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$customerRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CustomerRepositoryRef = AutoDisposeProviderRef<CustomerRepository>;
String _$customerServiceHash() => r'20d6b5621fc28c70e20bc6e90c70fbdee62f3887';

/// Provider for customer service
///
/// Copied from [customerService].
@ProviderFor(customerService)
final customerServiceProvider = AutoDisposeProvider<CustomerService>.internal(
  customerService,
  name: r'customerServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$customerServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CustomerServiceRef = AutoDisposeProviderRef<CustomerService>;
String _$customerNotifierHash() => r'1d50203190324139faebba77e3d940e6f4135c1a';

/// Provider for customer state management
///
/// Copied from [CustomerNotifier].
@ProviderFor(CustomerNotifier)
final customerNotifierProvider =
    AutoDisposeNotifierProvider<CustomerNotifier, CustomerState>.internal(
  CustomerNotifier.new,
  name: r'customerNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$customerNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CustomerNotifier = AutoDisposeNotifier<CustomerState>;
String _$singleCustomerNotifierHash() =>
    r'd88c068ec0eda0ed6a053c366b5733dbd30d0749';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$SingleCustomerNotifier
    extends BuildlessAutoDisposeNotifier<Customer?> {
  late final String customerId;

  Customer? build(
    String customerId,
  );
}

/// Provider for a single customer
///
/// Copied from [SingleCustomerNotifier].
@ProviderFor(SingleCustomerNotifier)
const singleCustomerNotifierProvider = SingleCustomerNotifierFamily();

/// Provider for a single customer
///
/// Copied from [SingleCustomerNotifier].
class SingleCustomerNotifierFamily extends Family<Customer?> {
  /// Provider for a single customer
  ///
  /// Copied from [SingleCustomerNotifier].
  const SingleCustomerNotifierFamily();

  /// Provider for a single customer
  ///
  /// Copied from [SingleCustomerNotifier].
  SingleCustomerNotifierProvider call(
    String customerId,
  ) {
    return SingleCustomerNotifierProvider(
      customerId,
    );
  }

  @override
  SingleCustomerNotifierProvider getProviderOverride(
    covariant SingleCustomerNotifierProvider provider,
  ) {
    return call(
      provider.customerId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'singleCustomerNotifierProvider';
}

/// Provider for a single customer
///
/// Copied from [SingleCustomerNotifier].
class SingleCustomerNotifierProvider
    extends AutoDisposeNotifierProviderImpl<SingleCustomerNotifier, Customer?> {
  /// Provider for a single customer
  ///
  /// Copied from [SingleCustomerNotifier].
  SingleCustomerNotifierProvider(
    String customerId,
  ) : this._internal(
          () => SingleCustomerNotifier()..customerId = customerId,
          from: singleCustomerNotifierProvider,
          name: r'singleCustomerNotifierProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$singleCustomerNotifierHash,
          dependencies: SingleCustomerNotifierFamily._dependencies,
          allTransitiveDependencies:
              SingleCustomerNotifierFamily._allTransitiveDependencies,
          customerId: customerId,
        );

  SingleCustomerNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.customerId,
  }) : super.internal();

  final String customerId;

  @override
  Customer? runNotifierBuild(
    covariant SingleCustomerNotifier notifier,
  ) {
    return notifier.build(
      customerId,
    );
  }

  @override
  Override overrideWith(SingleCustomerNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: SingleCustomerNotifierProvider._internal(
        () => create()..customerId = customerId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        customerId: customerId,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<SingleCustomerNotifier, Customer?>
      createElement() {
    return _SingleCustomerNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SingleCustomerNotifierProvider &&
        other.customerId == customerId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, customerId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SingleCustomerNotifierRef on AutoDisposeNotifierProviderRef<Customer?> {
  /// The parameter `customerId` of this provider.
  String get customerId;
}

class _SingleCustomerNotifierProviderElement
    extends AutoDisposeNotifierProviderElement<SingleCustomerNotifier,
        Customer?> with SingleCustomerNotifierRef {
  _SingleCustomerNotifierProviderElement(super.provider);

  @override
  String get customerId =>
      (origin as SingleCustomerNotifierProvider).customerId;
}

String _$customerAppointmentsNotifierHash() =>
    r'9560b2f25346bee59f22ac6c34f6002b4189ba2f';

abstract class _$CustomerAppointmentsNotifier
    extends BuildlessAutoDisposeNotifier<List<CustomerAppointment>> {
  late final String customerId;

  List<CustomerAppointment> build(
    String customerId,
  );
}

/// Provider for customer appointments
///
/// Copied from [CustomerAppointmentsNotifier].
@ProviderFor(CustomerAppointmentsNotifier)
const customerAppointmentsNotifierProvider =
    CustomerAppointmentsNotifierFamily();

/// Provider for customer appointments
///
/// Copied from [CustomerAppointmentsNotifier].
class CustomerAppointmentsNotifierFamily
    extends Family<List<CustomerAppointment>> {
  /// Provider for customer appointments
  ///
  /// Copied from [CustomerAppointmentsNotifier].
  const CustomerAppointmentsNotifierFamily();

  /// Provider for customer appointments
  ///
  /// Copied from [CustomerAppointmentsNotifier].
  CustomerAppointmentsNotifierProvider call(
    String customerId,
  ) {
    return CustomerAppointmentsNotifierProvider(
      customerId,
    );
  }

  @override
  CustomerAppointmentsNotifierProvider getProviderOverride(
    covariant CustomerAppointmentsNotifierProvider provider,
  ) {
    return call(
      provider.customerId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'customerAppointmentsNotifierProvider';
}

/// Provider for customer appointments
///
/// Copied from [CustomerAppointmentsNotifier].
class CustomerAppointmentsNotifierProvider
    extends AutoDisposeNotifierProviderImpl<CustomerAppointmentsNotifier,
        List<CustomerAppointment>> {
  /// Provider for customer appointments
  ///
  /// Copied from [CustomerAppointmentsNotifier].
  CustomerAppointmentsNotifierProvider(
    String customerId,
  ) : this._internal(
          () => CustomerAppointmentsNotifier()..customerId = customerId,
          from: customerAppointmentsNotifierProvider,
          name: r'customerAppointmentsNotifierProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$customerAppointmentsNotifierHash,
          dependencies: CustomerAppointmentsNotifierFamily._dependencies,
          allTransitiveDependencies:
              CustomerAppointmentsNotifierFamily._allTransitiveDependencies,
          customerId: customerId,
        );

  CustomerAppointmentsNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.customerId,
  }) : super.internal();

  final String customerId;

  @override
  List<CustomerAppointment> runNotifierBuild(
    covariant CustomerAppointmentsNotifier notifier,
  ) {
    return notifier.build(
      customerId,
    );
  }

  @override
  Override overrideWith(CustomerAppointmentsNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: CustomerAppointmentsNotifierProvider._internal(
        () => create()..customerId = customerId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        customerId: customerId,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<CustomerAppointmentsNotifier,
      List<CustomerAppointment>> createElement() {
    return _CustomerAppointmentsNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CustomerAppointmentsNotifierProvider &&
        other.customerId == customerId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, customerId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin CustomerAppointmentsNotifierRef
    on AutoDisposeNotifierProviderRef<List<CustomerAppointment>> {
  /// The parameter `customerId` of this provider.
  String get customerId;
}

class _CustomerAppointmentsNotifierProviderElement
    extends AutoDisposeNotifierProviderElement<CustomerAppointmentsNotifier,
        List<CustomerAppointment>> with CustomerAppointmentsNotifierRef {
  _CustomerAppointmentsNotifierProviderElement(super.provider);

  @override
  String get customerId =>
      (origin as CustomerAppointmentsNotifierProvider).customerId;
}

String _$customerStatsNotifierHash() =>
    r'4d1622ad73e5ab96353e426a9a3dfc43161aed13';

/// Provider for customer statistics
///
/// Copied from [CustomerStatsNotifier].
@ProviderFor(CustomerStatsNotifier)
final customerStatsNotifierProvider =
    AutoDisposeNotifierProvider<CustomerStatsNotifier, CustomerStats>.internal(
  CustomerStatsNotifier.new,
  name: r'customerStatsNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$customerStatsNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CustomerStatsNotifier = AutoDisposeNotifier<CustomerStats>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
