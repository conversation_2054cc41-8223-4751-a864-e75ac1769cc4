// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$initializeNotificationsHash() =>
    r'f3fac233999dca553daf5cc9eae31b7015b51f65';

/// Provider for notification service initialization (without FCM)
///
/// Copied from [initializeNotifications].
@ProviderFor(initializeNotifications)
final initializeNotificationsProvider =
    AutoDisposeFutureProvider<void>.internal(
  initializeNotifications,
  name: r'initializeNotificationsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$initializeNotificationsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef InitializeNotificationsRef = AutoDisposeFutureProviderRef<void>;
String _$deviceTokenHash() => r'1a3c0c6af3077b198a46f91ae8fd220c2c8e2dcb';

/// Provider for getting device FCM token
///
/// Copied from [deviceToken].
@ProviderFor(deviceToken)
final deviceTokenProvider = AutoDisposeFutureProvider<String?>.internal(
  deviceToken,
  name: r'deviceTokenProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$deviceTokenHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef DeviceTokenRef = AutoDisposeFutureProviderRef<String?>;
String _$notificationPermissionsHash() =>
    r'8cd77e094df779bd38c46abdf41c9e93760bcb8c';

/// Provider for checking notification permissions
///
/// Copied from [notificationPermissions].
@ProviderFor(notificationPermissions)
final notificationPermissionsProvider =
    AutoDisposeFutureProvider<bool>.internal(
  notificationPermissions,
  name: r'notificationPermissionsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$notificationPermissionsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef NotificationPermissionsRef = AutoDisposeFutureProviderRef<bool>;
String _$notificationSubscriptionsHash() =>
    r'e5261ef648d71ed678437dfe89e44f296d4be722';

/// Provider for managing notification subscriptions
///
/// Copied from [NotificationSubscriptions].
@ProviderFor(NotificationSubscriptions)
final notificationSubscriptionsProvider = AutoDisposeNotifierProvider<
    NotificationSubscriptions, Set<String>>.internal(
  NotificationSubscriptions.new,
  name: r'notificationSubscriptionsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$notificationSubscriptionsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NotificationSubscriptions = AutoDisposeNotifier<Set<String>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
