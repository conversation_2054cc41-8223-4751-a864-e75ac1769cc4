// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'language_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$languageApiServiceHash() =>
    r'5c5d1f202e3c3c5f1caf9f681d2ec906c6d7e86a';

/// Language API service provider
///
/// Copied from [languageApiService].
@ProviderFor(languageApiService)
final languageApiServiceProvider =
    AutoDisposeProvider<LanguageApiService>.internal(
  languageApiService,
  name: r'languageApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$languageApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef LanguageApiServiceRef = AutoDisposeProviderRef<LanguageApiService>;
String _$currentLocaleHash() => r'a362d82bdabfe9177a7b71b9592596ae275fdd5a';

/// Provider for current locale (for easy access in UI)
///
/// Copied from [currentLocale].
@ProviderFor(currentLocale)
final currentLocaleProvider = AutoDisposeProvider<Locale>.internal(
  currentLocale,
  name: r'currentLocaleProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentLocaleHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentLocaleRef = AutoDisposeProviderRef<Locale>;
String _$currentTextDirectionHash() =>
    r'61ad3f33ea5a4f7573ea0ce5729c98717cce77fa';

/// Provider for current text direction (for easy access in UI)
///
/// Copied from [currentTextDirection].
@ProviderFor(currentTextDirection)
final currentTextDirectionProvider =
    AutoDisposeProvider<TextDirection>.internal(
  currentTextDirection,
  name: r'currentTextDirectionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentTextDirectionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentTextDirectionRef = AutoDisposeProviderRef<TextDirection>;
String _$isRTLHash() => r'6561576b3e86fdab68b9adf81c745ff1bd5ddb91';

/// Provider for checking if current language is RTL
///
/// Copied from [isRTL].
@ProviderFor(isRTL)
final isRTLProvider = AutoDisposeProvider<bool>.internal(
  isRTL,
  name: r'isRTLProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$isRTLHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsRTLRef = AutoDisposeProviderRef<bool>;
String _$languageHash() => r'3a7337441505953823bc8903218b34a06b05dd94';

/// Language provider for managing app language state
///
/// Copied from [Language].
@ProviderFor(Language)
final languageProvider =
    AutoDisposeNotifierProvider<Language, LanguageState>.internal(
  Language.new,
  name: r'languageProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$languageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Language = AutoDisposeNotifier<LanguageState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
