#!/usr/bin/env node

/**
 * FCM Test using Firebase Functions approach
 * This creates a temporary Cloud Function to send the notification
 */

const fs = require('fs');
const { exec } = require('child_process');

console.log('🔥 FCM TESTING VIA FIREBASE FUNCTIONS');
console.log('=====================================\n');

const PROJECT_ID = 'dalti-prod';
const FCM_TOKEN = 'dX8_yF2uRyyVvcN-AmfjFl:APA91bFuCLThVntBVy-OHA_w12zbh1T7XYXcdV5nAKlxC1VRrFYfrMfHc5C3NjnhBIVW2ccv-QIBumqGpX5yMGQcgtuRNXp9RY3dJjK5mWGWKLYH9DtAuIM';

console.log(`📋 Project: ${PROJECT_ID}`);
console.log(`📱 Token: ${FCM_TOKEN.substring(0, 30)}...\n`);

// Create a temporary Cloud Function to send FCM
function createTempFunction() {
    console.log('1️⃣  Creating temporary Cloud Function...');
    
    // Create functions directory if it doesn't exist
    if (!fs.existsSync('functions')) {
        fs.mkdirSync('functions');
    }
    
    // Create package.json for the function
    const packageJson = {
        "name": "fcm-test-function",
        "version": "1.0.0",
        "main": "index.js",
        "dependencies": {
            "firebase-admin": "^12.0.0",
            "firebase-functions": "^5.0.0"
        }
    };
    
    fs.writeFileSync('functions/package.json', JSON.stringify(packageJson, null, 2));
    
    // Create the function code
    const functionCode = `
const { onRequest } = require('firebase-functions/v2/https');
const admin = require('firebase-admin');

// Initialize Firebase Admin
admin.initializeApp();

exports.testFCM = onRequest(async (req, res) => {
    try {
        console.log('🚀 FCM Test Function called');
        
        const message = {
            token: '${FCM_TOKEN}',
            notification: {
                title: '🎉 Firebase Function Test',
                body: 'Success! FCM working via Cloud Function'
            },
            data: {
                test_type: 'cloud_function',
                timestamp: new Date().toISOString(),
                source: 'firebase_function'
            },
            android: {
                priority: 'high',
                notification: {
                    channelId: 'dalti_provider_notifications',
                    icon: 'launcher_icon',
                    color: '#15424E',
                    sound: 'default'
                }
            }
        };
        
        console.log('📤 Sending FCM message:', JSON.stringify(message, null, 2));
        
        const response = await admin.messaging().send(message);
        
        console.log('✅ FCM message sent successfully:', response);
        
        res.json({
            success: true,
            messageId: response,
            message: 'FCM notification sent successfully!',
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        console.error('❌ FCM Error:', error);
        
        res.status(500).json({
            success: false,
            error: error.message,
            code: error.code,
            timestamp: new Date().toISOString()
        });
    }
});
`;
    
    fs.writeFileSync('functions/index.js', functionCode);
    
    console.log('✅ Temporary function created');
}

// Deploy the function
function deployFunction() {
    return new Promise((resolve, reject) => {
        console.log('\n2️⃣  Deploying function to Firebase...');
        console.log('⏳ This may take a few minutes...\n');
        
        exec('npx -y firebase-tools@latest deploy --only functions:testFCM', (error, stdout, stderr) => {
            if (error) {
                console.log('❌ Deploy failed:', error.message);
                console.log('stderr:', stderr);
                reject(error);
                return;
            }
            
            console.log('✅ Function deployed successfully!');
            console.log('Deploy output:', stdout);
            
            // Extract function URL from output
            const urlMatch = stdout.match(/https:\/\/[^\s]+/);
            if (urlMatch) {
                resolve(urlMatch[0]);
            } else {
                resolve(`https://us-central1-${PROJECT_ID}.cloudfunctions.net/testFCM`);
            }
        });
    });
}

// Call the function
function callFunction(functionUrl) {
    return new Promise((resolve) => {
        console.log(`\n3️⃣  Calling function: ${functionUrl}`);
        
        exec(`curl -X GET "${functionUrl}"`, (error, stdout, stderr) => {
            if (error) {
                console.log('❌ Function call failed:', error.message);
                resolve(false);
                return;
            }
            
            console.log('📥 Function response:', stdout);
            
            try {
                const response = JSON.parse(stdout);
                if (response.success) {
                    console.log('🎉 SUCCESS! FCM notification sent via Cloud Function!');
                    console.log(`📋 Message ID: ${response.messageId}`);
                    console.log('📱 Check your device now!');
                    resolve(true);
                } else {
                    console.log('❌ Function returned error:', response.error);
                    resolve(false);
                }
            } catch (e) {
                console.log('⚠️  Function response (raw):', stdout);
                resolve(false);
            }
        });
    });
}

// Cleanup function
function cleanup() {
    console.log('\n4️⃣  Cleaning up...');
    
    // Delete the function
    exec('npx -y firebase-tools@latest functions:delete testFCM --force', (error, stdout, stderr) => {
        if (error) {
            console.log('⚠️  Cleanup warning:', error.message);
        } else {
            console.log('✅ Function deleted');
        }
    });
    
    // Remove local files
    try {
        if (fs.existsSync('functions/index.js')) fs.unlinkSync('functions/index.js');
        if (fs.existsSync('functions/package.json')) fs.unlinkSync('functions/package.json');
        if (fs.existsSync('functions') && fs.readdirSync('functions').length === 0) {
            fs.rmdirSync('functions');
        }
        console.log('✅ Local files cleaned up');
    } catch (e) {
        console.log('⚠️  Cleanup warning:', e.message);
    }
}

// Alternative: Simple HTTP request approach
function testWithSimpleHTTP() {
    console.log('\n🔄 ALTERNATIVE: SIMPLE HTTP TEST');
    console.log('=================================\n');
    
    console.log('Since Cloud Functions might be complex, here\'s a simpler approach:');
    console.log('');
    console.log('1. Install Google Cloud CLI: https://cloud.google.com/sdk/docs/install');
    console.log('2. Run: gcloud auth login');
    console.log('3. Run: gcloud auth application-default print-access-token');
    console.log('4. Copy the access token');
    console.log('5. Run this curl command:\n');
    
    const curlCommand = `curl -X POST \\
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \\
  -H "Content-Type: application/json" \\
  -d '{
    "message": {
      "token": "${FCM_TOKEN}",
      "notification": {
        "title": "🚀 Direct API Test",
        "body": "Testing FCM via direct API call"
      },
      "data": {
        "test_type": "direct_api",
        "timestamp": "${new Date().toISOString()}"
      },
      "android": {
        "priority": "high",
        "notification": {
          "channel_id": "dalti_provider_notifications",
          "icon": "launcher_icon",
          "color": "#15424E"
        }
      }
    }
  }' \\
  "https://fcm.googleapis.com/v1/projects/${PROJECT_ID}/messages:send"`;
    
    console.log(curlCommand);
    console.log('\nReplace YOUR_ACCESS_TOKEN with the actual token from step 3.');
}

// Main execution
async function main() {
    try {
        console.log('🤔 CHOOSE YOUR APPROACH:\n');
        console.log('A) Deploy temporary Cloud Function (automated but complex)');
        console.log('B) Use direct HTTP API call (manual but simple)\n');
        
        // For now, let's show both approaches
        console.log('📋 APPROACH A: CLOUD FUNCTION');
        console.log('==============================');
        
        createTempFunction();
        
        console.log('\n⚠️  To continue with Cloud Function approach:');
        console.log('1. Run: npx -y firebase-tools@latest deploy --only functions:testFCM');
        console.log('2. Wait for deployment to complete');
        console.log('3. Call the function URL that gets printed');
        console.log('4. Check your device for notification');
        console.log('5. Clean up: npx -y firebase-tools@latest functions:delete testFCM --force\n');
        
        testWithSimpleHTTP();
        
        console.log('\n🎯 RECOMMENDATION:');
        console.log('Use Approach B (direct HTTP API) - it\'s simpler and faster!');
        
    } catch (error) {
        console.log(`❌ Error: ${error.message}`);
    }
}

main();
