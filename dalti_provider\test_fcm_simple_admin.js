#!/usr/bin/env node

/**
 * Simple FCM Test using Firebase Admin SDK
 * This is the easiest way to test FCM notifications
 */

console.log('🔥 SIMPLE FCM TEST WITH FIREBASE ADMIN SDK');
console.log('==========================================\n');

const FCM_TOKEN = 'dhOs2G1nT2CnKHqReUsat9:APA91bESgbZRzYD7YCqX-2UIal6BTduYJE4icIuaZ1WopPanbH8taK6PeroXwLiMsVdVYmQwjCnPgj-9dwcdtMvd52r_QyfOueSiK3LzgTrBODLYCDG25pw';

console.log(`📱 FCM Token: ${FCM_TOKEN.substring(0, 30)}...\n`);

// Check if firebase-admin is installed
try {
    require.resolve('firebase-admin');
    console.log('✅ firebase-admin package found');
} catch (e) {
    console.log('❌ firebase-admin package not found');
    console.log('');
    console.log('🔧 TO INSTALL:');
    console.log('Run: npm install firebase-admin');
    console.log('');
    console.log('🔧 ALTERNATIVE - MANUAL CURL TEST:');
    console.log('');
    console.log('1. Install Google Cloud CLI: https://cloud.google.com/sdk/docs/install');
    console.log('2. Run: gcloud auth login');
    console.log('3. Run: gcloud auth application-default print-access-token');
    console.log('4. Copy the access token and run this curl command:');
    console.log('');
    console.log(`curl -X POST \\
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \\
  -H "Content-Type: application/json" \\
  -d '{
    "message": {
      "token": "${FCM_TOKEN}",
      "notification": {
        "title": "🚀 Manual Test",
        "body": "Testing FCM manually"
      },
      "android": {
        "priority": "high",
        "notification": {
          "channel_id": "dalti_provider_notifications"
        }
      }
    }
  }' \\
  "https://fcm.googleapis.com/v1/projects/dalti-prod/messages:send"`);
    
    process.exit(1);
}

const admin = require('firebase-admin');

// Initialize Firebase Admin with service account
try {
    console.log('🔧 Initializing Firebase Admin...');

    // Check if service account key exists
    const fs = require('fs');
    if (fs.existsSync('service-account-key.json')) {
        console.log('✅ Service account key found');

        // Initialize with service account
        const serviceAccount = require('./service-account-key.json');
        admin.initializeApp({
            credential: admin.credential.cert(serviceAccount),
            projectId: 'dalti-prod'
        });

        console.log('✅ Firebase Admin initialized with service account');
    } else {
        // Try to initialize with default credentials (from Firebase CLI)
        admin.initializeApp({
            projectId: 'dalti-prod'
        });

        console.log('✅ Firebase Admin initialized with default credentials');
    }
    
} catch (error) {
    console.log('❌ Firebase Admin initialization failed:', error.message);
    console.log('');
    console.log('🔧 SOLUTIONS:');
    console.log('');
    console.log('OPTION 1 - Use Service Account:');
    console.log('1. Go to Firebase Console > Project Settings > Service Accounts');
    console.log('2. Click "Generate new private key"');
    console.log('3. Save as "service-account-key.json" in this directory');
    console.log('4. Run this script again');
    console.log('');
    console.log('OPTION 2 - Set Environment Variable:');
    console.log('1. Download service account key (as above)');
    console.log('2. Set environment variable:');
    console.log('   set GOOGLE_APPLICATION_CREDENTIALS=path\\to\\service-account-key.json');
    console.log('3. Run this script again');
    console.log('');
    console.log('OPTION 3 - Use Google Cloud CLI:');
    console.log('1. Install Google Cloud CLI');
    console.log('2. Run: gcloud auth application-default login');
    console.log('3. Run this script again');
    
    process.exit(1);
}

// Send FCM notification
async function sendNotification() {
    try {
        console.log('\n📤 Sending FCM notification...');
        
        const message = {
            token: FCM_TOKEN,
            notification: {
                title: '🎉 Admin SDK Test',
                body: 'Success! FCM working via Firebase Admin SDK'
            },
            data: {
                test_type: 'admin_sdk',
                timestamp: new Date().toISOString(),
                source: 'firebase_admin'
            },
            android: {
                priority: 'high',
                notification: {
                    channelId: 'dalti_provider_notifications',
                    icon: 'launcher_icon',
                    color: '#15424E',
                    sound: 'default'
                }
            }
        };
        
        console.log('📦 Message payload:');
        console.log(JSON.stringify(message, null, 2));
        console.log('');
        
        const response = await admin.messaging().send(message);
        
        console.log('🎉 SUCCESS! Notification sent successfully!');
        console.log(`📋 Message ID: ${response}`);
        console.log('📱 Check your Android device now!');
        console.log('');
        
        return true;
        
    } catch (error) {
        console.log('❌ Failed to send notification:', error.message);
        console.log('');
        
        // Provide specific error guidance
        if (error.code === 'messaging/registration-token-not-registered') {
            console.log('💡 SOLUTION: FCM token is invalid or expired');
            console.log('   • Generate a new FCM token in your app');
            console.log('   • Make sure the app is installed and has generated a token');
        } else if (error.code === 'messaging/invalid-registration-token') {
            console.log('💡 SOLUTION: FCM token format is invalid');
            console.log('   • Check the token format');
            console.log('   • Make sure you copied the complete token');
        } else if (error.code === 'messaging/mismatched-credential') {
            console.log('💡 SOLUTION: Project mismatch');
            console.log('   • Make sure the service account belongs to the correct project');
            console.log('   • Verify the FCM token was generated for this project');
        } else {
            console.log('💡 Error details:', error);
        }
        
        return false;
    }
}

// Main execution
async function main() {
    const success = await sendNotification();
    
    console.log('\n📊 FINAL SUMMARY');
    console.log('================\n');
    
    if (success) {
        console.log('✅ FCM Admin SDK is working correctly');
        console.log('✅ Authentication successful');
        console.log('✅ Project configuration correct');
        console.log('✅ FCM token is valid\n');
        
        console.log('🔍 IF NOTIFICATION DID NOT APPEAR:');
        console.log('   The issue is device-specific settings:');
        console.log('   • App notification permissions');
        console.log('   • Do Not Disturb mode');
        console.log('   • Battery optimization');
        console.log('   • App not handling FCM messages properly\n');
        
        console.log('🧪 NEXT STEPS:');
        console.log('   1. Check device notification settings');
        console.log('   2. Verify app is running and can receive messages');
        console.log('   3. Check Flutter console logs for message reception');
        console.log('   4. Test with app in foreground vs background');
        
    } else {
        console.log('❌ FCM Admin SDK test failed');
        console.log('🔧 Fix the authentication/configuration issues above');
    }
    
    console.log('\n🎯 CONCLUSION:');
    if (success) {
        console.log('   Firebase/FCM is working - focus on device settings');
    } else {
        console.log('   Fix Firebase authentication first');
    }
}

main().catch(console.error);
