// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'service_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$serviceApiServiceHash() => r'b267bd4083bb88bd6b114ca02c85f5dd9f4f32d1';

/// Service API service provider
///
/// Copied from [serviceApiService].
@ProviderFor(serviceApiService)
final serviceApiServiceProvider =
    AutoDisposeProvider<ServiceApiService>.internal(
  serviceApiService,
  name: r'serviceApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$serviceApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ServiceApiServiceRef = AutoDisposeProviderRef<ServiceApiService>;
String _$serviceRepositoryHash() => r'a65250ddf27d259f4cdcdb1deee2a4be23898b2a';

/// Service repository provider
///
/// Copied from [serviceRepository].
@ProviderFor(serviceRepository)
final serviceRepositoryProvider =
    AutoDisposeProvider<ServiceRepository>.internal(
  serviceRepository,
  name: r'serviceRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$serviceRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ServiceRepositoryRef = AutoDisposeProviderRef<ServiceRepository>;
String _$serviceNotifierHash() => r'abc9648326bdb4174454f93de205f15adbd25008';

/// Service provider for managing service state
///
/// Copied from [ServiceNotifier].
@ProviderFor(ServiceNotifier)
final serviceNotifierProvider =
    AutoDisposeNotifierProvider<ServiceNotifier, ServiceData>.internal(
  ServiceNotifier.new,
  name: r'serviceNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$serviceNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ServiceNotifier = AutoDisposeNotifier<ServiceData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
