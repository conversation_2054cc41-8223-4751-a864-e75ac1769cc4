{"project_info": {"project_number": "1060372851323", "project_id": "dalti-prod", "storage_bucket": "dalti-prod.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:1060372851323:android:97fbdd30154b22130690de", "android_client_info": {"package_name": "org.adscloud.dalti.customer"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyDDcsbxchQIzUvlxyr_vddecMVPhxbd6Lg"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:1060372851323:android:c968a0882c726c190690de", "android_client_info": {"package_name": "org.adscloud.dalti.provider"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyDDcsbxchQIzUvlxyr_vddecMVPhxbd6Lg"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}], "configuration_version": "1"}