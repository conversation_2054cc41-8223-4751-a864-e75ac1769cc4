#!/usr/bin/env node

/**
 * Debug SenderId Mismatch Issue
 * This script will help identify why we're getting SenderId mismatch
 */

console.log('🔍 DEBUGGING SENDERID MISMATCH');
console.log('==============================\n');

const FCM_TOKEN = 'dhOs2G1nT2CnKHqReUsat9:APA91bESgbZRzYD7YCqX-2UIal6BTduYJE4icIuaZ1WopPanbH8taK6PeroXwLiMsVdVYmQwjCnPgj-9dwcdtMvd52r_QyfOueSiK3LzgTrBODLYCDG25pw';

console.log('📋 ANALYZING TOKEN AND PROJECT CONFIGURATION\n');

// Analyze FCM token structure
function analyzeToken() {
    console.log('1️⃣  FCM TOKEN ANALYSIS');
    console.log('======================');
    
    console.log(`🔍 Full Token: ${FCM_TOKEN}`);
    console.log(`📏 Token Length: ${FCM_TOKEN.length}`);
    
    // Split token parts
    const parts = FCM_TOKEN.split(':');
    if (parts.length === 2) {
        console.log(`✅ Token Format: Valid (${parts[0].length} + ${parts[1].length} characters)`);
        console.log(`🔑 Token ID: ${parts[0]}`);
        console.log(`🔑 Token Secret: ${parts[1].substring(0, 20)}...`);
        
        // Check if it's a valid FCM token format
        if (parts[1].startsWith('APA91b')) {
            console.log('✅ Token Type: Valid FCM token (APA91b prefix)');
        } else {
            console.log('❌ Token Type: Invalid FCM token format');
        }
    } else {
        console.log('❌ Token Format: Invalid (should have exactly one colon)');
    }
    
    console.log('');
}

// Analyze project configuration
function analyzeProject() {
    console.log('2️⃣  PROJECT CONFIGURATION ANALYSIS');
    console.log('===================================');
    
    const fs = require('fs');
    
    // Check google-services.json
    if (fs.existsSync('android/app/google-services.json')) {
        try {
            const config = JSON.parse(fs.readFileSync('android/app/google-services.json', 'utf8'));
            
            console.log('📱 ANDROID APP CONFIGURATION:');
            console.log(`   Project ID: ${config.project_info.project_id}`);
            console.log(`   Project Number: ${config.project_info.project_number}`);
            console.log(`   App ID: ${config.client[0].client_info.mobilesdk_app_id}`);
            console.log(`   Package: ${config.client[0].client_info.android_client_info.package_name}`);
            
            // Extract sender ID from app ID
            const appId = config.client[0].client_info.mobilesdk_app_id;
            const senderIdMatch = appId.match(/^1:(\d+):/);
            if (senderIdMatch) {
                const senderId = senderIdMatch[1];
                console.log(`   📡 Sender ID: ${senderId}`);
                
                // Compare with project number
                if (senderId === config.project_info.project_number) {
                    console.log('   ✅ Sender ID matches project number');
                } else {
                    console.log('   ❌ Sender ID does NOT match project number');
                }
            }
            
        } catch (e) {
            console.log('❌ Invalid google-services.json:', e.message);
        }
    } else {
        console.log('❌ google-services.json not found');
    }
    
    console.log('');
    
    // Check service account
    if (fs.existsSync('service-account-key.json')) {
        try {
            const serviceAccount = JSON.parse(fs.readFileSync('service-account-key.json', 'utf8'));
            
            console.log('🔑 SERVICE ACCOUNT CONFIGURATION:');
            console.log(`   Project ID: ${serviceAccount.project_id}`);
            console.log(`   Client Email: ${serviceAccount.client_email}`);
            console.log(`   Client ID: ${serviceAccount.client_id}`);
            
        } catch (e) {
            console.log('❌ Invalid service-account-key.json:', e.message);
        }
    } else {
        console.log('❌ service-account-key.json not found');
    }
    
    console.log('');
}

// Test with different approaches
function suggestSolutions() {
    console.log('3️⃣  POTENTIAL SOLUTIONS');
    console.log('=======================');
    
    console.log('🔧 SOLUTION 1: Verify App Configuration');
    console.log('   • Make sure app is built with correct google-services.json');
    console.log('   • Rebuild and reinstall the app completely');
    console.log('   • Generate fresh FCM token after rebuild');
    console.log('');
    
    console.log('🔧 SOLUTION 2: Check Firebase Console');
    console.log('   • Go to Firebase Console > Project Settings > Cloud Messaging');
    console.log('   • Verify the Android app is properly registered');
    console.log('   • Check if there are multiple Android apps in the project');
    console.log('');
    
    console.log('🔧 SOLUTION 3: Use Legacy API (if available)');
    console.log('   • Try using the legacy FCM API with server key');
    console.log('   • Some projects still support legacy API');
    console.log('');
    
    console.log('🔧 SOLUTION 4: Direct HTTP Test');
    console.log('   • Test with curl using different authentication');
    console.log('   • Use API key instead of service account');
    console.log('');
}

// Create test with API key instead of service account
function createAPIKeyTest() {
    console.log('4️⃣  ALTERNATIVE TEST WITH API KEY');
    console.log('==================================');
    
    const fs = require('fs');
    
    // Get API key from google-services.json
    if (fs.existsSync('android/app/google-services.json')) {
        try {
            const config = JSON.parse(fs.readFileSync('android/app/google-services.json', 'utf8'));
            const apiKey = config.client[0].api_key[0].current_key;
            
            console.log('📋 CURL COMMAND WITH API KEY:');
            console.log('(This might work better than service account)');
            console.log('');
            
            const curlCommand = `curl -X POST \\
  -H "Authorization: key=${apiKey}" \\
  -H "Content-Type: application/json" \\
  -d '{
    "to": "${FCM_TOKEN}",
    "notification": {
      "title": "🔥 API Key Test",
      "body": "Testing with API key instead of service account"
    },
    "data": {
      "test_type": "api_key",
      "timestamp": "${new Date().toISOString()}"
    },
    "priority": "high"
  }' \\
  "https://fcm.googleapis.com/fcm/send"`;
            
            console.log(curlCommand);
            console.log('');
            console.log('💡 This uses the legacy FCM API which might still work');
            
        } catch (e) {
            console.log('❌ Could not extract API key from google-services.json');
        }
    }
}

// Main execution
function main() {
    analyzeToken();
    analyzeProject();
    suggestSolutions();
    createAPIKeyTest();
    
    console.log('🎯 NEXT STEPS:');
    console.log('1. Try the curl command above');
    console.log('2. If curl works: Issue is with service account authentication');
    console.log('3. If curl fails: Issue is with token/project configuration');
    console.log('4. Check Firebase Console for multiple Android apps');
    console.log('5. Consider rebuilding app with fresh google-services.json');
}

main();
