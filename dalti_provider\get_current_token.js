#!/usr/bin/env node

/**
 * Helper script to get current FCM token from running app
 */

console.log('📱 FCM TOKEN HELPER');
console.log('===================\n');

console.log('🔧 TO GET CURRENT FCM TOKEN:');
console.log('');
console.log('1. Make sure Dalti Provider app is running on your device');
console.log('2. Tap the "🚀 Test FCM" button on the dashboard');
console.log('3. Look for this line in Flutter console logs:');
console.log('   [FCM] Current Token: YOUR_TOKEN_HERE');
console.log('4. Copy the complete token');
console.log('5. Replace the token in test_fcm_simple_admin.js');
console.log('6. Run: node test_fcm_simple_admin.js');
console.log('');

console.log('🎯 WHAT TO LOOK FOR IN FLUTTER CONSOLE:');
console.log('');
console.log('✅ GOOD - Look for these lines:');
console.log('   [FCM] ✅ FCM Token Available: dX8_...');
console.log('   [FCM] Current Token: FULL_TOKEN_HERE');
console.log('');
console.log('❌ BAD - If you see:');
console.log('   [FCM] ❌ JWT Token NOT Available');
console.log('   [FCM] Token Sent to Server: false');
console.log('');

console.log('🔍 TROUBLESHOOTING:');
console.log('');
console.log('If no token appears:');
console.log('• Restart the app completely');
console.log('• Check internet connection');
console.log('• Verify google-services.json is correct');
console.log('• Check Firebase project configuration');
console.log('');

console.log('📋 CURRENT PROJECT INFO:');
console.log('• Project ID: dalti-prod');
console.log('• Project Number: 1060372851323');
console.log('• Package: org.adscloud.dalti.provider');
console.log('');

console.log('🚀 ONCE YOU HAVE THE NEW TOKEN:');
console.log('1. Edit test_fcm_simple_admin.js');
console.log('2. Replace: const FCM_TOKEN = \'PASTE_NEW_TOKEN_HERE\';');
console.log('3. Run: node test_fcm_simple_admin.js');
console.log('4. Check your device for notification!');
