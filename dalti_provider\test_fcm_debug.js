#!/usr/bin/env node

/**
 * Automated FCM Debugging Script
 * This script tests all FCM components and identifies issues
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 AUTOMATED FCM DEBUGGING SCRIPT');
console.log('=====================================\n');

let issues = [];
let warnings = [];
let success = [];

function addIssue(message) {
    issues.push(`❌ ${message}`);
    console.log(`❌ ${message}`);
}

function addWarning(message) {
    warnings.push(`⚠️  ${message}`);
    console.log(`⚠️  ${message}`);
}

function addSuccess(message) {
    success.push(`✅ ${message}`);
    console.log(`✅ ${message}`);
}

// 1. Check Android Manifest Configuration
console.log('1️⃣  CHECKING ANDROID MANIFEST...\n');

const manifestPath = 'android/app/src/main/AndroidManifest.xml';
if (fs.existsSync(manifestPath)) {
    const manifest = fs.readFileSync(manifestPath, 'utf8');
    
    // Check FCM permissions
    if (manifest.includes('com.google.android.c2dm.permission.RECEIVE')) {
        addSuccess('FCM permission found in manifest');
    } else {
        addIssue('Missing FCM permission: com.google.android.c2dm.permission.RECEIVE');
    }
    
    // Check notification permissions
    if (manifest.includes('android.permission.POST_NOTIFICATIONS')) {
        addSuccess('Notification permission found in manifest');
    } else {
        addWarning('Missing notification permission (required for Android 13+)');
    }
    
    // Check FCM service declaration
    if (manifest.includes('com.google.firebase.MESSAGING_EVENT')) {
        addSuccess('FCM service intent filter found');
    } else {
        addIssue('Missing FCM service intent filter');
    }
    
    // Check notification channel configuration
    const channelMatch = manifest.match(/default_notification_channel_id[\s\S]*?value="([^"]+)"/);
    if (channelMatch) {
        const channelId = channelMatch[1];
        addSuccess(`Notification channel configured: ${channelId}`);

        // Store channel ID for later verification
        global.manifestChannelId = channelId;
    } else {
        addWarning('No default notification channel configured');
    }
    
    // Check notification icon
    if (manifest.includes('default_notification_icon')) {
        addSuccess('Default notification icon configured');
    } else {
        addWarning('No default notification icon configured');
    }
    
} else {
    addIssue('AndroidManifest.xml not found');
}

console.log('\n2️⃣  CHECKING FIREBASE CONFIGURATION...\n');

// Check google-services.json
const googleServicesPath = 'android/app/google-services.json';
if (fs.existsSync(googleServicesPath)) {
    try {
        const googleServices = JSON.parse(fs.readFileSync(googleServicesPath, 'utf8'));
        
        if (googleServices.project_info && googleServices.project_info.project_id) {
            addSuccess(`Firebase project: ${googleServices.project_info.project_id}`);
        }
        
        if (googleServices.client && googleServices.client[0] && googleServices.client[0].client_info) {
            const packageName = googleServices.client[0].client_info.android_client_info.package_name;
            addSuccess(`Package name: ${packageName}`);
        }
        
    } catch (e) {
        addIssue('Invalid google-services.json format');
    }
} else {
    addIssue('google-services.json not found in android/app/');
}

// Check Firebase options
const firebaseOptionsPath = 'lib/firebase_options.dart';
if (fs.existsSync(firebaseOptionsPath)) {
    const firebaseOptions = fs.readFileSync(firebaseOptionsPath, 'utf8');
    
    if (firebaseOptions.includes('messagingSenderId')) {
        addSuccess('Firebase messaging sender ID configured');
    } else {
        addWarning('No messaging sender ID found in firebase_options.dart');
    }
    
    if (firebaseOptions.includes('projectId')) {
        addSuccess('Firebase project ID configured');
    } else {
        addIssue('No project ID found in firebase_options.dart');
    }
} else {
    addIssue('firebase_options.dart not found');
}

console.log('\n3️⃣  CHECKING FLUTTER FCM SERVICE...\n');

// Check Firebase messaging service
const fcmServicePath = 'lib/core/services/firebase_messaging_service.dart';
if (fs.existsSync(fcmServicePath)) {
    const fcmService = fs.readFileSync(fcmServicePath, 'utf8');
    
    // Check notification channel consistency
    const channelMatches = fcmService.match(/'([^']+)'/g);
    const channelIds = channelMatches?.filter(match => 
        match.includes('channel') || match.includes('notification')
    ) || [];
    
    if (global.manifestChannelId) {
        const serviceUsesCorrectChannel = fcmService.includes(`'${global.manifestChannelId}'`);
        if (serviceUsesCorrectChannel) {
            addSuccess(`Service uses correct channel ID: ${global.manifestChannelId}`);
        } else {
            addIssue(`Channel ID mismatch! Manifest: ${global.manifestChannelId}, Service may use different ID`);
        }
    }
    
    // Check message handlers
    if (fcmService.includes('FirebaseMessaging.onMessage.listen')) {
        addSuccess('Foreground message handler configured');
    } else {
        addIssue('Missing foreground message handler');
    }
    
    if (fcmService.includes('FirebaseMessaging.onBackgroundMessage')) {
        addSuccess('Background message handler configured');
    } else {
        addWarning('Background message handler not found in service');
    }
    
    // Check local notifications
    if (fcmService.includes('FlutterLocalNotificationsPlugin')) {
        addSuccess('Local notifications plugin integrated');
    } else {
        addIssue('Local notifications plugin not found');
    }
    
    // Check notification channel creation
    if (fcmService.includes('createNotificationChannel')) {
        addSuccess('Notification channel creation implemented');
    } else {
        addIssue('Missing notification channel creation');
    }
    
} else {
    addIssue('Firebase messaging service not found');
}

console.log('\n4️⃣  CHECKING MAIN.DART INITIALIZATION...\n');

// Check main.dart
const mainPath = 'lib/main.dart';
if (fs.existsSync(mainPath)) {
    const mainContent = fs.readFileSync(mainPath, 'utf8');
    
    if (mainContent.includes('Firebase.initializeApp')) {
        addSuccess('Firebase initialization found in main.dart');
    } else {
        addIssue('Firebase initialization missing in main.dart');
    }
    
    if (mainContent.includes('FirebaseMessaging.onBackgroundMessage')) {
        addSuccess('Background message handler registered in main.dart');
    } else {
        addIssue('Background message handler not registered in main.dart');
    }
    
    if (mainContent.includes('FirebaseMessagingService.initialize')) {
        addSuccess('FCM service initialization found');
    } else {
        addWarning('FCM service initialization not found in main.dart');
    }
} else {
    addIssue('main.dart not found');
}

console.log('\n5️⃣  CHECKING PUBSPEC DEPENDENCIES...\n');

// Check pubspec.yaml
const pubspecPath = 'pubspec.yaml';
if (fs.existsSync(pubspecPath)) {
    const pubspec = fs.readFileSync(pubspecPath, 'utf8');
    
    const requiredDeps = [
        'firebase_core',
        'firebase_messaging',
        'flutter_local_notifications'
    ];
    
    requiredDeps.forEach(dep => {
        if (pubspec.includes(dep)) {
            addSuccess(`Dependency found: ${dep}`);
        } else {
            addIssue(`Missing dependency: ${dep}`);
        }
    });
} else {
    addIssue('pubspec.yaml not found');
}

// Generate Summary Report
console.log('\n📊 DEBUGGING SUMMARY');
console.log('====================\n');

console.log(`✅ Success: ${success.length} items`);
console.log(`⚠️  Warnings: ${warnings.length} items`);
console.log(`❌ Issues: ${issues.length} items\n`);

if (issues.length === 0) {
    console.log('🎉 NO CRITICAL ISSUES FOUND!');
    console.log('FCM configuration appears to be correct.');
    console.log('\nIf notifications still don\'t work, the issue might be:');
    console.log('- Device-specific settings (Do Not Disturb, battery optimization)');
    console.log('- Network connectivity');
    console.log('- Firebase project configuration');
    console.log('- Token registration timing');
} else {
    console.log('🚨 CRITICAL ISSUES FOUND:');
    issues.forEach(issue => console.log(issue));
    console.log('\nFix these issues first, then test again.');
}

if (warnings.length > 0) {
    console.log('\n⚠️  WARNINGS TO ADDRESS:');
    warnings.forEach(warning => console.log(warning));
}

// Save detailed report
const report = {
    timestamp: new Date().toISOString(),
    success: success,
    warnings: warnings,
    issues: issues,
    summary: {
        successCount: success.length,
        warningCount: warnings.length,
        issueCount: issues.length,
        status: issues.length === 0 ? 'PASS' : 'FAIL'
    }
};

fs.writeFileSync('fcm_debug_report.json', JSON.stringify(report, null, 2));
console.log('\n📄 Detailed report saved to: fcm_debug_report.json');

process.exit(issues.length === 0 ? 0 : 1);
