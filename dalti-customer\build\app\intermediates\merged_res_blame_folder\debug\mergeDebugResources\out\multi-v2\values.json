{"logs": [{"outputFile": "org.adscloud.dalti.customer.app-mergeDebugResources-51:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\91df1ec7fb930f87f9c88e367152244b\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,127,267,268,269,270,271,272,273,334,335,336,379,380,435,437,447,448,454,455,456,1529,1713,1716,1722,1728,1731,1737,1741,1744,1751,1757,1760,1766,1771,1776,1783,1785,1791,1797,1805,1810,1817,1822,1828,1832,1839,1843,1849,1855,1858,1862,1863,2781,2796,2935,2973,3117,3305,3323,3387,3397,3407,3414,3420,3524,3693,3710", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,6667,16332,16396,16451,16519,16586,16651,16708,20175,20223,20271,22579,22642,27568,27674,28547,28591,28926,29065,29115,97321,111059,111164,111409,111747,111893,112233,112445,112608,113015,113353,113476,113815,114054,114311,114682,114742,115080,115366,115815,116107,116495,116800,117144,117389,117719,117926,118194,118467,118611,118812,118859,161603,162126,168912,170213,175230,181140,181768,183693,183975,184280,184542,184802,188318,194613,195143", "endLines": "63,127,267,268,269,270,271,272,273,334,335,336,379,380,435,437,447,450,454,455,456,1545,1715,1721,1727,1730,1736,1740,1743,1750,1756,1759,1765,1770,1775,1782,1784,1790,1796,1804,1809,1816,1821,1827,1831,1838,1842,1848,1854,1857,1861,1862,1863,2785,2806,2954,2976,3126,3312,3386,3396,3406,3413,3419,3462,3536,3709,3726", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2268,6731,16391,16446,16514,16581,16646,16703,16760,20218,20266,20327,22637,22700,27601,27726,28586,28726,29060,29110,29158,98754,111159,111404,111742,111888,112228,112440,112603,113010,113348,113471,113810,114049,114306,114677,114737,115075,115361,115810,116102,116490,116795,117139,117384,117714,117921,118189,118462,118606,118807,118854,118910,161783,162522,169636,170357,175557,181383,183688,183970,184275,184537,184797,186220,188765,195138,195706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4a61e7ad15945e1b3aaaa7815dd5b350\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "369", "startColumns": "4", "startOffsets": "21997", "endColumns": "42", "endOffsets": "22035"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fd004af28618635ed2c68077f32c9eb6\\transformed\\jetified-play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "378,425", "startColumns": "4,4", "startOffsets": "22511,26275", "endColumns": "67,166", "endOffsets": "22574,26437"}}, {"source": "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1546,1550", "startColumns": "4,4", "startOffsets": "98759,98940", "endLines": "1549,1552", "endColumns": "12,12", "endOffsets": "98935,99104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7a32d1638c100bef6cccc6fe9e637ce5\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "239,240,241,249,250,251,330,3469", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "14357,14416,14464,15131,15206,15282,19996,186440", "endLines": "239,240,241,249,250,251,330,3488", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14411,14459,14515,15201,15277,15349,20057,187230"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3ea11e2a256a4e60e39c47432c63089a\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "409", "startColumns": "4", "startOffsets": "24641", "endColumns": "82", "endOffsets": "24719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\46a2f623aa225cef07cf72877fccfef1\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "85,86,87,88,228,229,436,438,439,440", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3874,3932,3998,4061,13553,13624,27606,27731,27798,27877", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3927,3993,4056,4118,13619,13691,27669,27793,27872,27941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e5672b6e30b06127e0c3f67c9f39e57a\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "29,71,72,91,92,123,125,230,231,232,233,234,235,236,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,331,332,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,381,410,411,412,413,414,415,416,453,1975,1976,1981,1984,1989,2133,2134,2790,2807,2977,3012,3042,3075", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2768,2840,4238,4303,6393,6515,13696,13766,13834,13906,13976,14037,14111,15354,15415,15476,15538,15602,15664,15725,15793,15893,15953,16019,16092,16161,16218,16270,17430,17502,17578,17643,17702,17761,17821,17881,17941,18001,18061,18121,18181,18241,18301,18361,18420,18480,18540,18600,18660,18720,18780,18840,18900,18960,19020,19079,19139,19199,19258,19317,19376,19435,19494,20062,20097,20683,20738,20801,20856,20914,20970,21028,21089,21152,21209,21260,21318,21368,21429,21486,21552,21586,21621,22705,24724,24791,24863,24932,25001,25075,25147,28855,127884,128001,128268,128561,128828,140267,140339,161923,162527,170362,172168,173168,173850", "endLines": "29,71,72,91,92,123,125,230,231,232,233,234,235,236,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,331,332,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,381,410,411,412,413,414,415,416,453,1975,1979,1981,1987,1989,2133,2134,2795,2816,3011,3032,3074,3080", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2835,2923,4298,4364,6457,6573,13761,13829,13901,13971,14032,14106,14179,15410,15471,15533,15597,15659,15720,15788,15888,15948,16014,16087,16156,16213,16265,16327,17497,17573,17638,17697,17756,17816,17876,17936,17996,18056,18116,18176,18236,18296,18356,18415,18475,18535,18595,18655,18715,18775,18835,18895,18955,19015,19074,19134,19194,19253,19312,19371,19430,19489,19548,20092,20127,20733,20796,20851,20909,20965,21023,21084,21147,21204,21255,21313,21363,21424,21481,21547,21581,21616,21651,22770,24786,24858,24927,24996,25070,25142,25230,28921,127996,128197,128373,128757,128952,140334,140401,162121,162823,172163,172844,173845,174012"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\44720affda7edf70af4d44695d5acbda\\transformed\\jetified-core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "368", "startColumns": "4", "startOffsets": "21954", "endColumns": "42", "endOffsets": "21992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fd831584ed04ca4fba5608ab323cf35f\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "93,94,95,96,97,98,99,100,417,418,419,420,421,422,423,424,426,427,428,429,430,431,432,433,434,3127,3537", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4369,4459,4539,4629,4719,4799,4880,4960,25235,25340,25521,25646,25753,25933,26056,26172,26442,26630,26735,26916,27041,27216,27364,27427,27489,175562,188770", "endLines": "93,94,95,96,97,98,99,100,417,418,419,420,421,422,423,424,426,427,428,429,430,431,432,433,434,3139,3555", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4454,4534,4624,4714,4794,4875,4955,5035,25335,25516,25641,25748,25928,26051,26167,26270,26625,26730,26911,27036,27211,27359,27422,27484,27563,175872,189182"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8bd0941f79f0634dd381e68d38176bc6\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "326,342,373,3033,3038", "startColumns": "4,4,4,4,4", "startOffsets": "19818,20572,22204,172849,173019", "endLines": "326,342,373,3037,3041", "endColumns": "56,64,63,24,24", "endOffsets": "19870,20632,22263,173014,173163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2a345cfcb0f72806fc2741662bceb8fd\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "126,133,139,274,275,276,277,376,1980,1982,1983,1988,1990", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6578,7084,7487,16765,16818,16871,16924,22394,128202,128378,128500,128762,128957", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "6662,7150,7555,16813,16866,16919,16972,22449,128263,128495,128556,128823,129019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e83d8c5cd0d2af91e865bb02d93869ba\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3603,3675,3747,3820,3877,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3670,3742,3815,3872,3930,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "4,27,28,59,60,61,62,64,65,66,67,69,70,74,75,77,78,79,80,81,82,83,84,89,90,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,129,130,131,132,134,135,136,137,138,140,141,142,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,237,238,242,243,244,245,246,247,248,278,279,280,281,282,283,284,285,321,322,323,324,329,337,338,343,367,374,375,377,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,452,457,458,459,460,461,462,470,471,475,479,483,488,494,501,505,509,514,518,522,526,530,534,538,544,548,554,558,564,568,573,577,580,584,590,594,600,604,610,613,617,621,625,629,633,634,635,636,639,642,645,648,652,653,654,655,656,659,661,663,665,670,671,675,681,685,686,688,699,700,704,710,714,715,716,720,747,751,752,756,784,954,980,1151,1177,1208,1216,1222,1236,1258,1263,1268,1278,1287,1296,1300,1307,1315,1322,1323,1332,1335,1338,1342,1346,1350,1353,1354,1359,1364,1374,1379,1386,1392,1393,1396,1400,1405,1407,1409,1412,1415,1417,1421,1424,1431,1434,1437,1441,1443,1447,1449,1451,1453,1457,1465,1473,1485,1491,1500,1503,1514,1517,1518,1523,1524,1553,1622,1692,1693,1703,1712,1864,1866,1870,1873,1876,1879,1882,1885,1888,1891,1895,1898,1901,1904,1908,1911,1915,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1941,1943,1944,1945,1946,1947,1948,1949,1950,1952,1953,1955,1956,1958,1960,1961,1963,1964,1965,1966,1967,1968,1970,1971,1972,1973,1974,1991,1993,1995,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2011,2012,2013,2014,2015,2016,2018,2022,2026,2027,2028,2029,2030,2031,2035,2036,2037,2038,2040,2042,2044,2046,2048,2049,2050,2051,2053,2055,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2071,2072,2073,2074,2076,2078,2079,2081,2082,2084,2086,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2101,2102,2103,2104,2106,2107,2108,2109,2110,2112,2114,2116,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2129,2130,2131,2132,2138,2213,2216,2219,2222,2236,2253,2295,2324,2351,2360,2422,2786,2817,2955,3081,3105,3111,3140,3161,3285,3313,3319,3463,3489,3556,3627,3727,3747,3802,3814,3840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2273,2354,2415,2490,2613,2690,2978,3063,3196,3272,3348,3425,3503,3609,3715,3794,4123,4180,5040,5114,5189,5254,5320,5380,5441,5513,5586,5653,5721,5780,5839,5898,5957,6016,6070,6124,6177,6231,6285,6339,6784,6858,6937,7010,7155,7227,7299,7372,7429,7560,7634,7708,7834,7906,7979,8049,8120,8180,8241,8310,8379,8449,8523,8599,8663,8740,8816,8893,8958,9027,9104,9179,9248,9316,9393,9459,9520,9617,9682,9751,9850,9921,9980,10038,10095,10154,10218,10289,10361,10433,10505,10577,10644,10712,10780,10839,10902,10966,11056,11147,11207,11273,11340,11406,11476,11540,11593,11660,11721,11788,11901,11959,12022,12087,12152,12227,12300,12372,12421,12482,12543,12604,12666,12730,12794,12858,12923,12986,13046,13107,13173,13232,13292,13354,13425,13485,14184,14270,14520,14610,14697,14785,14867,14950,15040,16977,17029,17087,17132,17198,17262,17319,17376,19553,19610,19658,19707,19962,20332,20379,20637,21922,22268,22332,22454,22775,22849,22919,22997,23051,23121,23206,23254,23300,23361,23424,23490,23554,23625,23688,23753,23817,23878,23939,23991,24064,24138,24207,24282,24356,24430,24571,28802,29163,29241,29331,29419,29515,29605,30187,30276,30523,30804,31056,31341,31734,32211,32433,32655,32931,33158,33388,33618,33848,34078,34305,34724,34950,35375,35605,36033,36252,36535,36743,36874,37101,37527,37752,38179,38400,38825,38945,39221,39522,39846,40137,40451,40588,40719,40824,41066,41233,41437,41645,41916,42028,42140,42245,42362,42576,42722,42862,42948,43296,43384,43630,44048,44297,44379,44477,45069,45169,45421,45845,46100,46194,46283,46520,48544,48786,48888,49141,51297,61829,63345,73976,75504,77261,77887,78307,79368,80633,80889,81125,81672,82166,82771,82969,83549,84113,84488,84606,85144,85301,85497,85770,86026,86196,86337,86401,86766,87133,87809,88073,88411,88764,88858,89044,89350,89612,89737,89864,90103,90314,90433,90626,90803,91258,91439,91561,91820,91933,92120,92222,92329,92458,92733,93241,93737,94614,94908,95478,95627,96359,96531,96615,96951,97043,99109,104355,109744,109806,110384,110968,118915,119028,119257,119417,119569,119740,119906,120075,120242,120405,120648,120818,120991,121162,121436,121635,121840,122170,122254,122350,122446,122544,122644,122746,122848,122950,123052,123154,123254,123350,123462,123591,123714,123845,123976,124074,124188,124282,124422,124556,124652,124764,124864,124980,125076,125188,125288,125428,125564,125728,125858,126016,126166,126307,126451,126586,126698,126848,126976,127104,127240,127372,127502,127632,127744,129024,129170,129314,129452,129518,129608,129684,129788,129878,129980,130088,130196,130296,130376,130468,130566,130676,130754,130860,130952,131056,131166,131288,131451,131608,131688,131788,131878,131988,132078,132319,132413,132519,132611,132711,132823,132937,133053,133169,133263,133377,133489,133591,133711,133833,133915,134019,134139,134265,134363,134457,134545,134657,134773,134895,135007,135182,135298,135384,135476,135588,135712,135779,135905,135973,136101,136245,136373,136442,136537,136652,136765,136864,136973,137084,137195,137296,137401,137501,137631,137722,137845,137939,138051,138137,138241,138337,138425,138543,138647,138751,138877,138965,139073,139173,139263,139373,139457,139559,139643,139697,139761,139867,139953,140063,140147,140551,143167,143285,143400,143480,143841,144427,145831,147175,148536,148924,151699,161788,162828,169641,174017,174768,175030,175877,176256,180534,181388,181617,186225,187235,189187,191587,195711,196455,198586,198926,200237", "endLines": "4,27,28,59,60,61,62,64,65,66,67,69,70,74,75,77,78,79,80,81,82,83,84,89,90,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,129,130,131,132,134,135,136,137,138,140,141,142,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,237,238,242,243,244,245,246,247,248,278,279,280,281,282,283,284,285,321,322,323,324,329,337,338,343,367,374,375,377,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,452,457,458,459,460,461,469,470,474,478,482,487,493,500,504,508,513,517,521,525,529,533,537,543,547,553,557,563,567,572,576,579,583,589,593,599,603,609,612,616,620,624,628,632,633,634,635,638,641,644,647,651,652,653,654,655,658,660,662,664,669,670,674,680,684,685,687,698,699,703,709,713,714,715,719,746,750,751,755,783,953,979,1150,1176,1207,1215,1221,1235,1257,1262,1267,1277,1286,1295,1299,1306,1314,1321,1322,1331,1334,1337,1341,1345,1349,1352,1353,1358,1363,1373,1378,1385,1391,1392,1395,1399,1404,1406,1408,1411,1414,1416,1420,1423,1430,1433,1436,1440,1442,1446,1448,1450,1452,1456,1464,1472,1484,1490,1499,1502,1513,1516,1517,1522,1523,1528,1621,1691,1692,1702,1711,1712,1865,1869,1872,1875,1878,1881,1884,1887,1890,1894,1897,1900,1903,1907,1910,1914,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1940,1942,1943,1944,1945,1946,1947,1948,1949,1951,1952,1954,1955,1957,1959,1960,1962,1963,1964,1965,1966,1967,1969,1970,1971,1972,1973,1974,1992,1994,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2010,2011,2012,2013,2014,2015,2017,2021,2025,2026,2027,2028,2029,2030,2034,2035,2036,2037,2039,2041,2043,2045,2047,2048,2049,2050,2052,2054,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2070,2071,2072,2073,2075,2077,2078,2080,2081,2083,2085,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2100,2101,2102,2103,2105,2106,2107,2108,2109,2111,2113,2115,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2129,2130,2131,2132,2212,2215,2218,2221,2235,2241,2262,2323,2350,2359,2421,2780,2789,2844,2972,3104,3110,3116,3160,3284,3304,3318,3322,3468,3523,3567,3692,3746,3801,3813,3839,3846", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2133,2195,2349,2410,2485,2561,2685,2763,3058,3140,3267,3343,3420,3498,3604,3710,3789,3869,4175,4233,5109,5184,5249,5315,5375,5436,5508,5581,5648,5716,5775,5834,5893,5952,6011,6065,6119,6172,6226,6280,6334,6388,6853,6932,7005,7079,7222,7294,7367,7424,7482,7629,7703,7778,7901,7974,8044,8115,8175,8236,8305,8374,8444,8518,8594,8658,8735,8811,8888,8953,9022,9099,9174,9243,9311,9388,9454,9515,9612,9677,9746,9845,9916,9975,10033,10090,10149,10213,10284,10356,10428,10500,10572,10639,10707,10775,10834,10897,10961,11051,11142,11202,11268,11335,11401,11471,11535,11588,11655,11716,11783,11896,11954,12017,12082,12147,12222,12295,12367,12416,12477,12538,12599,12661,12725,12789,12853,12918,12981,13041,13102,13168,13227,13287,13349,13420,13480,13548,14265,14352,14605,14692,14780,14862,14945,15035,15126,17024,17082,17127,17193,17257,17314,17371,17425,19605,19653,19702,19753,19991,20374,20423,20678,21949,22327,22389,22506,22844,22914,22992,23046,23116,23201,23249,23295,23356,23419,23485,23549,23620,23683,23748,23812,23873,23934,23986,24059,24133,24202,24277,24351,24425,24566,24636,28850,29236,29326,29414,29510,29600,30182,30271,30518,30799,31051,31336,31729,32206,32428,32650,32926,33153,33383,33613,33843,34073,34300,34719,34945,35370,35600,36028,36247,36530,36738,36869,37096,37522,37747,38174,38395,38820,38940,39216,39517,39841,40132,40446,40583,40714,40819,41061,41228,41432,41640,41911,42023,42135,42240,42357,42571,42717,42857,42943,43291,43379,43625,44043,44292,44374,44472,45064,45164,45416,45840,46095,46189,46278,46515,48539,48781,48883,49136,51292,61824,63340,73971,75499,77256,77882,78302,79363,80628,80884,81120,81667,82161,82766,82964,83544,84108,84483,84601,85139,85296,85492,85765,86021,86191,86332,86396,86761,87128,87804,88068,88406,88759,88853,89039,89345,89607,89732,89859,90098,90309,90428,90621,90798,91253,91434,91556,91815,91928,92115,92217,92324,92453,92728,93236,93732,94609,94903,95473,95622,96354,96526,96610,96946,97038,97316,104350,109739,109801,110379,110963,111054,119023,119252,119412,119564,119735,119901,120070,120237,120400,120643,120813,120986,121157,121431,121630,121835,122165,122249,122345,122441,122539,122639,122741,122843,122945,123047,123149,123249,123345,123457,123586,123709,123840,123971,124069,124183,124277,124417,124551,124647,124759,124859,124975,125071,125183,125283,125423,125559,125723,125853,126011,126161,126302,126446,126581,126693,126843,126971,127099,127235,127367,127497,127627,127739,127879,129165,129309,129447,129513,129603,129679,129783,129873,129975,130083,130191,130291,130371,130463,130561,130671,130749,130855,130947,131051,131161,131283,131446,131603,131683,131783,131873,131983,132073,132314,132408,132514,132606,132706,132818,132932,133048,133164,133258,133372,133484,133586,133706,133828,133910,134014,134134,134260,134358,134452,134540,134652,134768,134890,135002,135177,135293,135379,135471,135583,135707,135774,135900,135968,136096,136240,136368,136437,136532,136647,136760,136859,136968,137079,137190,137291,137396,137496,137626,137717,137840,137934,138046,138132,138236,138332,138420,138538,138642,138746,138872,138960,139068,139168,139258,139368,139452,139554,139638,139692,139756,139862,139948,140058,140142,140262,143162,143280,143395,143475,143836,144069,144939,147170,148531,148919,151694,161598,161918,164180,170208,174763,175025,175225,176251,180529,181135,181612,181763,186435,188313,189494,194608,196450,198581,198921,200232,200435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a75d60fd8877511dc386f45edb6c47d1\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "327,328,333,340,341,362,363,364,365,366", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19875,19915,20132,20470,20525,21656,21710,21762,21811,21872", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "19910,19957,20170,20520,20567,21705,21757,21806,21867,21917"}}, {"source": "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\build\\app\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,138,242,352,472,574", "endColumns": "82,103,109,119,101,70", "endOffsets": "133,237,347,467,569,640"}, "to": {"startLines": "442,443,444,445,446,451", "startColumns": "4,4,4,4,4,4", "startOffsets": "28028,28111,28215,28325,28445,28731", "endColumns": "82,103,109,119,101,70", "endOffsets": "28106,28210,28320,28440,28542,28797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4656a7b7476adc5d08fdd0fc696ebf6d\\transformed\\jetified-firebase-messaging-25.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "441", "startColumns": "4", "startOffsets": "27946", "endColumns": "81", "endOffsets": "28023"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7b627014e4a89e473a30de52ef9e08c9\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,325,2242,2248,3568,3576,3591", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,19758,144074,144269,189499,189781,190395", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,325,2247,2252,3575,3590,3606", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,19813,144264,144422,189776,190390,191044"}}, {"source": "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\android\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "5,12,11,4,3,8", "startColumns": "4,4,4,4,4,4", "startOffsets": "193,421,369,139,90,279", "endColumns": "46,49,50,52,47,50", "endOffsets": "235,466,415,187,133,325"}, "to": {"startLines": "68,73,76,124,128,143", "startColumns": "4,4,4,4,4,4", "startOffsets": "2566,2928,3145,6462,6736,7783", "endColumns": "46,49,50,52,47,50", "endOffsets": "2608,2973,3191,6510,6779,7829"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6abff8940ad166ef2db48ebfd914525c\\transformed\\jetified-activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "339,370", "startColumns": "4,4", "startOffsets": "20428,22040", "endColumns": "41,59", "endOffsets": "20465,22095"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2af0a837f45526bdf9a3ab404bf19b99\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "372", "startColumns": "4", "startOffsets": "22154", "endColumns": "49", "endOffsets": "22199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f0fecf08ca85e93113c5917f08b4ef0c\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "371", "startColumns": "4", "startOffsets": "22100", "endColumns": "53", "endOffsets": "22149"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\81df4349873e6830084f7e9d3aa47c88\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2263,2279,2285,3607,3623", "startColumns": "4,4,4,4,4", "startOffsets": "144944,145369,145547,191049,191460", "endLines": "2278,2284,2294,3622,3626", "endColumns": "24,24,24,24,24", "endOffsets": "145364,145542,145826,191455,191582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\683dc414081aa9a9600bafdfef78c5b3\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2135,2845,2851", "startColumns": "4,4,4,4", "startOffsets": "164,140406,164185,164396", "endLines": "3,2137,2850,2934", "endColumns": "60,12,24,24", "endOffsets": "220,140546,164391,168907"}}]}]}