# PowerShell script to remove all print statements from Dart files
# This script will process all .dart files in the lib directory

param(
    [string]$LibPath = "lib",
    [switch]$DryRun = $false
)

Write-Host "🎯 Starting print statement removal process..." -ForegroundColor Green
Write-Host "📁 Target directory: $LibPath" -ForegroundColor Cyan
Write-Host "🔍 Dry run mode: $DryRun" -ForegroundColor Yellow

# Get all Dart files
$dartFiles = Get-ChildItem -Path $LibPath -Recurse -Filter "*.dart"
Write-Host "📄 Found $($dartFiles.Count) Dart files" -ForegroundColor Cyan

$totalPrintStatements = 0
$processedFiles = 0

foreach ($file in $dartFiles) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Count print statements before removal
    $printMatches = [regex]::Matches($content, "print\(")
    $printCount = $printMatches.Count
    
    if ($printCount -gt 0) {
        Write-Host "🔧 Processing: $($file.Name) ($printCount print statements)" -ForegroundColor Yellow
        
        # Remove print statements with various patterns
        # Pattern 1: Single line print statements
        $content = $content -replace "^\s*print\([^;]*\);\s*$", ""
        
        # Pattern 2: Multi-line print statements
        $content = $content -replace "(?ms)^\s*print\(\s*\n.*?\n\s*\);\s*$", ""
        
        # Pattern 3: Print statements with string concatenation
        $content = $content -replace "(?ms)^\s*print\([^)]*\);\s*$", ""
        
        # Pattern 4: Remove empty lines left behind
        $content = $content -replace "(?m)^\s*\n", "`n"
        
        # Pattern 5: Remove multiple consecutive empty lines
        $content = $content -replace "(?m)\n\s*\n\s*\n", "`n`n"
        
        if (-not $DryRun) {
            Set-Content -Path $file.FullName -Value $content -NoNewline
        }
        
        $totalPrintStatements += $printCount
        $processedFiles++
    }
}

Write-Host "✅ Process completed!" -ForegroundColor Green
Write-Host "📊 Files processed: $processedFiles" -ForegroundColor Cyan
Write-Host "🗑️ Print statements removed: $totalPrintStatements" -ForegroundColor Cyan

if ($DryRun) {
    Write-Host "⚠️ This was a dry run. No files were modified." -ForegroundColor Yellow
    Write-Host "💡 Run without -DryRun to actually remove print statements." -ForegroundColor Yellow
}
