#!/usr/bin/env node

/**
 * Manual FCM Test - You provide the access token
 * This bypasses Firebase CLI issues
 */

const https = require('https');

console.log('🔥 MANUAL FCM TESTING');
console.log('=====================\n');

const PROJECT_ID = 'dalti-prod';
const FCM_TOKEN = 'dX8_yF2uRyyVvcN-AmfjFl:APA91bFuCLThVntBVy-OHA_w12zbh1T7XYXcdV5nAKlxC1VRrFYfrMfHc5C3NjnhBIVW2ccv-QIBumqGpX5yMGQcgtuRNXp9RY3dJjK5mWGWKLYH9DtAuIM';

// You need to get this token manually
const ACCESS_TOKEN = 'PASTE_YOUR_ACCESS_TOKEN_HERE';

console.log(`📋 Project ID: ${PROJECT_ID}`);
console.log(`📱 FCM Token: ${FCM_TOKEN.substring(0, 30)}...\n`);

if (ACCESS_TOKEN === 'PASTE_YOUR_ACCESS_TOKEN_HERE') {
    console.log('❌ ACCESS TOKEN NOT PROVIDED\n');
    console.log('🔧 TO GET ACCESS TOKEN:');
    console.log('');
    console.log('METHOD 1 - Google Cloud CLI:');
    console.log('1. Install Google Cloud CLI: https://cloud.google.com/sdk/docs/install');
    console.log('2. Run: gcloud auth login');
    console.log('3. Run: gcloud auth application-default print-access-token');
    console.log('4. Copy the token and paste it in this script\n');
    
    console.log('METHOD 2 - Firebase CLI (if working):');
    console.log('1. Run: npx -y firebase-tools@latest login');
    console.log('2. Run: npx -y firebase-tools@latest auth:print-access-token');
    console.log('3. Copy the token and paste it in this script\n');
    
    console.log('METHOD 3 - OAuth Playground:');
    console.log('1. Go to: https://developers.google.com/oauthplayground/');
    console.log('2. Select "Firebase Cloud Messaging API v1"');
    console.log('3. Authorize and get access token\n');
    
    console.log('Then edit this file and replace:');
    console.log('const ACCESS_TOKEN = \'PASTE_YOUR_ACCESS_TOKEN_HERE\';');
    console.log('with:');
    console.log('const ACCESS_TOKEN = \'your_actual_token_here\';');
    
    process.exit(1);
}

// Send FCM notification
function sendNotification() {
    return new Promise((resolve) => {
        console.log('📤 Sending FCM notification...');
        
        const payload = {
            message: {
                token: FCM_TOKEN,
                notification: {
                    title: "🚀 Manual Token Test",
                    body: "Testing FCM with manually provided access token"
                },
                data: {
                    test_type: "manual_token",
                    timestamp: new Date().toISOString(),
                    source: "manual_script"
                },
                android: {
                    priority: "high",
                    notification: {
                        channel_id: "dalti_provider_notifications",
                        icon: "launcher_icon",
                        color: "#15424E",
                        sound: "default"
                    }
                }
            }
        };
        
        const postData = JSON.stringify(payload);
        
        const options = {
            hostname: 'fcm.googleapis.com',
            port: 443,
            path: `/v1/projects/${PROJECT_ID}/messages:send`,
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${ACCESS_TOKEN}`,
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };
        
        console.log(`🔗 Endpoint: https://fcm.googleapis.com/v1/projects/${PROJECT_ID}/messages:send`);
        console.log(`🔑 Token: ${ACCESS_TOKEN.substring(0, 20)}...`);
        console.log('📦 Payload:');
        console.log(JSON.stringify(payload, null, 2));
        console.log('\n📡 Sending request...\n');
        
        const req = https.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                console.log(`📊 HTTP Status: ${res.statusCode}`);
                console.log(`📥 Response Headers:`, res.headers);
                console.log(`📥 Response Body: ${responseData}\n`);
                
                if (res.statusCode === 200) {
                    console.log('🎉 SUCCESS! Notification sent successfully!');
                    console.log('📱 Check your Android device NOW!');
                    console.log('👀 Look for the notification in the notification panel');
                    
                    try {
                        const response = JSON.parse(responseData);
                        console.log(`📋 Message ID: ${response.name}`);
                    } catch (e) {
                        // Response parsing failed, but request succeeded
                    }
                    
                    console.log('\n✅ CONCLUSION: FCM API is working correctly');
                    console.log('✅ If notification appeared: Everything is working!');
                    console.log('❌ If notification did NOT appear: Device settings issue');
                    
                } else {
                    console.log('❌ FAILED to send notification');
                    
                    try {
                        const error = JSON.parse(responseData);
                        console.log(`💥 Error Code: ${error.error.code}`);
                        console.log(`💥 Error Message: ${error.error.message}`);
                        console.log(`💥 Error Status: ${error.error.status}`);
                        
                        // Provide specific guidance
                        switch (error.error.code) {
                            case 401:
                                console.log('\n💡 SOLUTION: Access token expired or invalid');
                                console.log('   Get a fresh access token using the methods above');
                                break;
                            case 403:
                                console.log('\n💡 SOLUTION: Permission denied');
                                console.log(`   Make sure you have FCM permissions for project: ${PROJECT_ID}`);
                                console.log('   Check if you\'re logged in with the correct Google account');
                                break;
                            case 404:
                                console.log('\n💡 SOLUTION: Project not found');
                                console.log(`   Verify project ID: ${PROJECT_ID}`);
                                console.log('   Make sure the project exists and you have access');
                                break;
                            case 400:
                                console.log('\n💡 SOLUTION: Invalid request');
                                console.log('   Check FCM token format and payload structure');
                                console.log('   Verify the FCM token is still valid');
                                break;
                        }
                    } catch (e) {
                        console.log(`💥 Raw error response: ${responseData}`);
                    }
                }
                
                resolve(res.statusCode === 200);
            });
        });
        
        req.on('error', (error) => {
            console.log(`❌ Network error: ${error.message}`);
            console.log('🔧 Check your internet connection');
            resolve(false);
        });
        
        req.write(postData);
        req.end();
    });
}

// Main execution
async function main() {
    const success = await sendNotification();
    
    console.log('\n📊 FINAL DEBUGGING SUMMARY');
    console.log('===========================\n');
    
    if (success) {
        console.log('✅ FCM HTTP v1 API is working');
        console.log('✅ Authentication is successful');
        console.log('✅ Project configuration is correct');
        console.log('✅ Network connectivity is good\n');
        
        console.log('🔍 IF NOTIFICATION DID NOT APPEAR ON DEVICE:');
        console.log('   The issue is 100% device-specific settings:');
        console.log('   1. App notification permissions disabled');
        console.log('   2. Do Not Disturb mode active');
        console.log('   3. Battery optimization blocking the app');
        console.log('   4. Manufacturer-specific power management');
        console.log('   5. App not properly handling FCM messages\n');
        
        console.log('🧪 DEVICE TESTING CHECKLIST:');
        console.log('   • Settings > Apps > Dalti Provider > Notifications > Allow');
        console.log('   • Turn off Do Not Disturb mode');
        console.log('   • Settings > Battery > Battery Optimization > Dalti Provider > Don\'t optimize');
        console.log('   • Keep app open in foreground during test');
        console.log('   • Check Flutter console logs for message reception');
        
    } else {
        console.log('❌ FCM API call failed');
        console.log('🔧 Fix the authentication/configuration issues above');
        console.log('🔧 Make sure you have the correct access token');
        console.log('🔧 Verify you have permissions for the Firebase project');
    }
    
    console.log('\n🎯 NEXT STEPS:');
    console.log('1. If API succeeded: Focus on device notification settings');
    console.log('2. If API failed: Get a fresh access token and try again');
    console.log('3. Check Flutter app console logs for FCM message reception');
    console.log('4. Test with app in both foreground and background states');
}

main();
