org.adscloud.dalti.customer.app-jetified-datastore-release-0 C:\Users\<USER>\.gradle\caches\8.12\transforms\0955710b688470dcac9773610ea34397\transformed\jetified-datastore-release\res
org.adscloud.dalti.customer.app-jetified-savedstate-ktx-1.2.1-1 C:\Users\<USER>\.gradle\caches\8.12\transforms\12fc76cf587460e361a6458e09c0efd0\transformed\jetified-savedstate-ktx-1.2.1\res
org.adscloud.dalti.customer.app-jetified-datastore-core-release-2 C:\Users\<USER>\.gradle\caches\8.12\transforms\198514471fb0d60e8d8970edf27db216\transformed\jetified-datastore-core-release\res
org.adscloud.dalti.customer.app-jetified-lifecycle-livedata-core-ktx-2.7.0-3 C:\Users\<USER>\.gradle\caches\8.12\transforms\252645ed4c270e1e74cc53855c24b582\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
org.adscloud.dalti.customer.app-media-1.1.0-4 C:\Users\<USER>\.gradle\caches\8.12\transforms\2a345cfcb0f72806fc2741662bceb8fd\transformed\media-1.1.0\res
org.adscloud.dalti.customer.app-lifecycle-viewmodel-2.7.0-5 C:\Users\<USER>\.gradle\caches\8.12\transforms\2af0a837f45526bdf9a3ab404bf19b99\transformed\lifecycle-viewmodel-2.7.0\res
org.adscloud.dalti.customer.app-jetified-firebase-common-22.0.0-6 C:\Users\<USER>\.gradle\caches\8.12\transforms\332745d27e38ec1773168a826c04b248\transformed\jetified-firebase-common-22.0.0\res
org.adscloud.dalti.customer.app-jetified-startup-runtime-1.1.1-7 C:\Users\<USER>\.gradle\caches\8.12\transforms\3ea11e2a256a4e60e39c47432c63089a\transformed\jetified-startup-runtime-1.1.1\res
org.adscloud.dalti.customer.app-jetified-core-viewtree-1.0.0-8 C:\Users\<USER>\.gradle\caches\8.12\transforms\44720affda7edf70af4d44695d5acbda\transformed\jetified-core-viewtree-1.0.0\res
org.adscloud.dalti.customer.app-jetified-firebase-messaging-25.0.0-9 C:\Users\<USER>\.gradle\caches\8.12\transforms\4656a7b7476adc5d08fdd0fc696ebf6d\transformed\jetified-firebase-messaging-25.0.0\res
org.adscloud.dalti.customer.app-browser-1.8.0-10 C:\Users\<USER>\.gradle\caches\8.12\transforms\46a2f623aa225cef07cf72877fccfef1\transformed\browser-1.8.0\res
org.adscloud.dalti.customer.app-lifecycle-runtime-2.7.0-11 C:\Users\<USER>\.gradle\caches\8.12\transforms\4a61e7ad15945e1b3aaaa7815dd5b350\transformed\lifecycle-runtime-2.7.0\res
org.adscloud.dalti.customer.app-jetified-annotation-experimental-1.4.1-12 C:\Users\<USER>\.gradle\caches\8.12\transforms\562062fec8951ed1c65807c0e151a00f\transformed\jetified-annotation-experimental-1.4.1\res
org.adscloud.dalti.customer.app-jetified-core-ktx-1.16.0-13 C:\Users\<USER>\.gradle\caches\8.12\transforms\5c5e2db31e2cb52208f296ef3ba28786\transformed\jetified-core-ktx-1.16.0\res
org.adscloud.dalti.customer.app-coordinatorlayout-1.0.0-14 C:\Users\<USER>\.gradle\caches\8.12\transforms\683dc414081aa9a9600bafdfef78c5b3\transformed\coordinatorlayout-1.0.0\res
org.adscloud.dalti.customer.app-jetified-activity-1.10.1-15 C:\Users\<USER>\.gradle\caches\8.12\transforms\6abff8940ad166ef2db48ebfd914525c\transformed\jetified-activity-1.10.1\res
org.adscloud.dalti.customer.app-lifecycle-livedata-core-2.7.0-16 C:\Users\<USER>\.gradle\caches\8.12\transforms\72a0f5884b31725573813a1d402cd3af\transformed\lifecycle-livedata-core-2.7.0\res
org.adscloud.dalti.customer.app-jetified-window-java-1.2.0-17 C:\Users\<USER>\.gradle\caches\8.12\transforms\7a04a17a9205dde36c97e2cebc53785f\transformed\jetified-window-java-1.2.0\res
org.adscloud.dalti.customer.app-recyclerview-1.0.0-18 C:\Users\<USER>\.gradle\caches\8.12\transforms\7a32d1638c100bef6cccc6fe9e637ce5\transformed\recyclerview-1.0.0\res
org.adscloud.dalti.customer.app-jetified-lifecycle-runtime-ktx-2.7.0-19 C:\Users\<USER>\.gradle\caches\8.12\transforms\7a4a5aa4e0530b48b72e553b63a9e57c\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
org.adscloud.dalti.customer.app-jetified-window-1.2.0-20 C:\Users\<USER>\.gradle\caches\8.12\transforms\7b627014e4a89e473a30de52ef9e08c9\transformed\jetified-window-1.2.0\res
org.adscloud.dalti.customer.app-jetified-appcompat-resources-1.1.0-21 C:\Users\<USER>\.gradle\caches\8.12\transforms\81df4349873e6830084f7e9d3aa47c88\transformed\jetified-appcompat-resources-1.1.0\res
org.adscloud.dalti.customer.app-jetified-ads-adservices-1.1.0-beta11-22 C:\Users\<USER>\.gradle\caches\8.12\transforms\8676ccfa02022d8c4fd8349174181d7f\transformed\jetified-ads-adservices-1.1.0-beta11\res
org.adscloud.dalti.customer.app-fragment-1.7.1-23 C:\Users\<USER>\.gradle\caches\8.12\transforms\8bd0941f79f0634dd381e68d38176bc6\transformed\fragment-1.7.1\res
org.adscloud.dalti.customer.app-preference-1.2.1-24 C:\Users\<USER>\.gradle\caches\8.12\transforms\91df1ec7fb930f87f9c88e367152244b\transformed\preference-1.2.1\res
org.adscloud.dalti.customer.app-core-runtime-2.2.0-25 C:\Users\<USER>\.gradle\caches\8.12\transforms\97ea082c33c0d8ddd1d8a9f2b3e3aa9e\transformed\core-runtime-2.2.0\res
org.adscloud.dalti.customer.app-jetified-ads-adservices-java-1.1.0-beta11-26 C:\Users\<USER>\.gradle\caches\8.12\transforms\9bf21ff439b49d46362d4f516e0942ed\transformed\jetified-ads-adservices-java-1.1.0-beta11\res
org.adscloud.dalti.customer.app-jetified-activity-ktx-1.10.1-27 C:\Users\<USER>\.gradle\caches\8.12\transforms\a00b2ffb168256e1f7909371942771a3\transformed\jetified-activity-ktx-1.10.1\res
org.adscloud.dalti.customer.app-jetified-datastore-preferences-release-28 C:\Users\<USER>\.gradle\caches\8.12\transforms\a63ba0e07425a4341d8e29eb6ffaf952\transformed\jetified-datastore-preferences-release\res
org.adscloud.dalti.customer.app-transition-1.4.1-29 C:\Users\<USER>\.gradle\caches\8.12\transforms\a75d60fd8877511dc386f45edb6c47d1\transformed\transition-1.4.1\res
org.adscloud.dalti.customer.app-slidingpanelayout-1.2.0-30 C:\Users\<USER>\.gradle\caches\8.12\transforms\b1a6f442694d8b8451d1325b391aa326\transformed\slidingpanelayout-1.2.0\res
org.adscloud.dalti.customer.app-jetified-lifecycle-viewmodel-ktx-2.7.0-31 C:\Users\<USER>\.gradle\caches\8.12\transforms\b7fb70a772e6e4086a8a2922bead3483\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
org.adscloud.dalti.customer.app-jetified-profileinstaller-1.4.0-32 C:\Users\<USER>\.gradle\caches\8.12\transforms\bc86da57d79a6a6cd40d22a2d30ebba5\transformed\jetified-profileinstaller-1.4.0\res
org.adscloud.dalti.customer.app-jetified-lifecycle-process-2.7.0-33 C:\Users\<USER>\.gradle\caches\8.12\transforms\cd0f01ff653eb56f5facfc3f1b8cfba8\transformed\jetified-lifecycle-process-2.7.0\res
org.adscloud.dalti.customer.app-core-1.16.0-34 C:\Users\<USER>\.gradle\caches\8.12\transforms\e5672b6e30b06127e0c3f67c9f39e57a\transformed\core-1.16.0\res
org.adscloud.dalti.customer.app-appcompat-1.1.0-35 C:\Users\<USER>\.gradle\caches\8.12\transforms\e83d8c5cd0d2af91e865bb02d93869ba\transformed\appcompat-1.1.0\res
org.adscloud.dalti.customer.app-jetified-core-1.0.0-36 C:\Users\<USER>\.gradle\caches\8.12\transforms\ea5ff790cdccfe7507d43c622f58f158\transformed\jetified-core-1.0.0\res
org.adscloud.dalti.customer.app-lifecycle-livedata-2.7.0-37 C:\Users\<USER>\.gradle\caches\8.12\transforms\ea7109a9d8e5e495a604f2b7ab622000\transformed\lifecycle-livedata-2.7.0\res
org.adscloud.dalti.customer.app-localbroadcastmanager-1.1.0-38 C:\Users\<USER>\.gradle\caches\8.12\transforms\ef42274e179ffb6fba3193797a2c11eb\transformed\localbroadcastmanager-1.1.0\res
org.adscloud.dalti.customer.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-39 C:\Users\<USER>\.gradle\caches\8.12\transforms\f0c1ae5e76019819a7e0e4829ea391b8\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
org.adscloud.dalti.customer.app-jetified-savedstate-1.2.1-40 C:\Users\<USER>\.gradle\caches\8.12\transforms\f0fecf08ca85e93113c5917f08b4ef0c\transformed\jetified-savedstate-1.2.1\res
org.adscloud.dalti.customer.app-jetified-fragment-ktx-1.7.1-41 C:\Users\<USER>\.gradle\caches\8.12\transforms\f236a334407ff65d4d6a2c605b6188b8\transformed\jetified-fragment-ktx-1.7.1\res
org.adscloud.dalti.customer.app-jetified-play-services-basement-18.5.0-42 C:\Users\<USER>\.gradle\caches\8.12\transforms\fd004af28618635ed2c68077f32c9eb6\transformed\jetified-play-services-basement-18.5.0\res
org.adscloud.dalti.customer.app-jetified-play-services-base-18.5.0-43 C:\Users\<USER>\.gradle\caches\8.12\transforms\fd831584ed04ca4fba5608ab323cf35f\transformed\jetified-play-services-base-18.5.0\res
org.adscloud.dalti.customer.app-jetified-tracing-1.2.0-44 C:\Users\<USER>\.gradle\caches\8.12\transforms\ff04e08a871aaa501f751ba0fb108cde\transformed\jetified-tracing-1.2.0\res
org.adscloud.dalti.customer.app-jetified-security-crypto-1.1.0-alpha06-45 C:\Users\<USER>\.gradle\caches\8.12\transforms\ff1ed85d45a352cf930cb79118de2dda\transformed\jetified-security-crypto-1.1.0-alpha06\res
org.adscloud.dalti.customer.app-debug-46 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\debug\res
org.adscloud.dalti.customer.app-main-47 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\android\app\src\main\res
org.adscloud.dalti.customer.app-pngs-48 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\app\generated\res\pngs\debug
org.adscloud.dalti.customer.app-res-49 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\app\generated\res\processDebugGoogleServices
org.adscloud.dalti.customer.app-resValues-50 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\app\generated\res\resValues\debug
org.adscloud.dalti.customer.app-packageDebugResources-51 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
org.adscloud.dalti.customer.app-packageDebugResources-52 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
org.adscloud.dalti.customer.app-debug-53 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\app\intermediates\merged_res\debug\mergeDebugResources
org.adscloud.dalti.customer.app-debug-54 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_analytics\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.customer.app-debug-55 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_core\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.customer.app-debug-56 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\firebase_messaging\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.customer.app-debug-57 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\flutter_local_notifications\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.customer.app-debug-58 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.customer.app-debug-59 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\flutter_secure_storage\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.customer.app-debug-60 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\geolocator_android\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.customer.app-debug-61 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\image_picker_android\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.customer.app-debug-62 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\package_info_plus\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.customer.app-debug-63 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\path_provider_android\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.customer.app-debug-64 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\permission_handler_android\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.customer.app-debug-65 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\shared_preferences_android\intermediates\packaged_res\debug\packageDebugResources
org.adscloud.dalti.customer.app-debug-66 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti-customer\build\url_launcher_android\intermediates\packaged_res\debug\packageDebugResources
