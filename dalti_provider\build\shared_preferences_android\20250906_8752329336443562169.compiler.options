"-Xallow-no-source-files" "-classpath" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\build\\shared_preferences_android\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\77a57776ade161208265d2b8c7b03d2c\\transformed\\jetified-flutter_embedding_debug-1.0.0-a8bfdfc394deaed5c57bd45a64ac4294dc976a72.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\07ae740731d7f93f5a87b66ca993c3da\\transformed\\preference-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f06091369df495ba50c9eab253364fb\\transformed\\appcompat-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4f37a61e97a6249c605d68ebd5e83287\\transformed\\jetified-fragment-ktx-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3d2fc16653ff98449e5afda5923b5294\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\56322cf13eddb854df66176aabbefed1\\transformed\\recyclerview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a5ca728c7f5322551615c83922c08532\\transformed\\jetified-activity-ktx-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ecbcc9dbce6fb0fe70316be126fefcdc\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a5b326287faf6e4b36d384bc44e4880\\transformed\\legacy-support-core-ui-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f8d40cc1a3a43cbe8421b49985121849\\transformed\\legacy-support-core-utils-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4e8891bc8975cd0da761148d12055588\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\264f23c0bbd9a358e77e0d77bfb90a44\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\122a5969b1985d7be42b02b84febb01c\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e959f2268e1bb67cffd06b0a8ba87a74\\transformed\\jetified-lifecycle-viewmodel-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\59d3a81edc03c19d474990ec419400ae\\transformed\\jetified-lifecycle-runtime-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c54b6d82d1b065ea68e10bc84323ab31\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3e246e8ad82edd48d2190c3b67b7dfb6\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a856922fde46c1b802a84bd265d26d70\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7354b1e4fbd90b40efc379347b5df712\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\05891960b183a9002606bccd729cfe8b\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b2a5ab7350279a278131e85368417be9\\transformed\\jetified-appcompat-resources-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\11e992f10fd35a1ac4e9a3d961c3c409\\transformed\\drawerlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f396bb6911777a6fb35f06b6a17665a\\transformed\\slidingpanelayout-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46395b174309b12e01805fcff5c1bf46\\transformed\\coordinatorlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\862c5dfdb425deb81fcd2e855736c4e2\\transformed\\customview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1e01b269438fbba87fa9d47dfb65db6c\\transformed\\vectordrawable-animated-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2fe5bb3c9738e776debf8642115f99e9\\transformed\\vectordrawable-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f9780cd26532b3e1f4a0b0c5e33e1438\\transformed\\swiperefreshlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\959086e43fbef7fa412423324c213877\\transformed\\asynclayoutinflater-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fe592f154d8c37db6fad3a97d539d9a9\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cd4ef2dbd3b3751aeb17cc405443b49f\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b79a76a33a4a075e3fbc3c7022e528ad\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1f60f2a559aa68f208bd72232f85694\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e59fefeb2ef861f1050d323105c3f7d2\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8befa92e040b06116a5e17b765584eec\\transformed\\jetified-datastore-preferences-core-jvm-1.1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7105cb0ca29258bc73a6c74b25a69f0a\\transformed\\jetified-datastore-core-okio-jvm-1.1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ebc34a10454305a2be21e492b6e74b0a\\transformed\\jetified-datastore-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f3e07aad3d17d6356e4c62186231f0de\\transformed\\jetified-datastore-preferences-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9737347608e90a531855db9452198bd3\\transformed\\jetified-datastore-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9477a2da014eba6378d9c9d67148a6e5\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ba780a5812ed6599a2e023c249ade1ea\\transformed\\jetified-savedstate-ktx-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c2947395e8fab9a91193e5f88065cea3\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1ec3eb1e95c63b36e433bc3002b38324\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\26dd23b1db5107ea2a6b2c998df02a06\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37b6bc05e0859ce15cf6712a78ebec6d\\transformed\\jetified-collection-ktx-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f77f4ca10c06bd0ab4852effaa3243af\\transformed\\cursoradapter-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0fea24358ee5bc4b09b9740bbbd64b31\\transformed\\interpolator-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d134e644a0a815e16b83361b99353a2d\\transformed\\documentfile-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\27b7a21a60431d8a2d518eae8a628825\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6b1cffc19af3c994f037b1b5b4fc22a2\\transformed\\print-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\697ef3aafec4246b58c6e73092f613f3\\transformed\\jetified-annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a53240c83d227dd7d48a45bf800cdb21\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e0c81cd42481197d80547d471a2a7238\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\77c430995f895519fb0ea04de763b709\\transformed\\jetified-okio-jvm-3.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f610fcac379a581a5f8497542d8bc04b\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb2c46fb12030802b25931c1e7cc6c27\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6df012aed8a88dd0a8d20285d34538d6\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8521e63fa3973b03a64640ba8087db9c\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4d531023fd29aeab28529d21bfc03a0d\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ec6b4f7eedded958dc57536e52e45c61\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a7d3cf6280bab24c0706cae5087f7396\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-36\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\build\\shared_preferences_android\\tmp\\kotlin-classes\\debug" "-jvm-target" "11" "-module-name" "shared_preferences_android_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\LegacySharedPreferencesPlugin.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\Messages.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\SharedPreferencesListEncoder.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\MessagesAsync.g.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\SharedPreferencesPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\StringListObjectInputStream.kt"