#!/usr/bin/env node

/**
 * Complete FCM Diagnostic Tool
 * Comprehensive testing and debugging for FCM issues
 */

const fs = require('fs');
const { exec } = require('child_process');

console.log('🔬 COMPLETE FCM DIAGNOSTIC TOOL');
console.log('================================\n');

let diagnostics = {
    configuration: { pass: 0, fail: 0, warn: 0, items: [] },
    environment: { pass: 0, fail: 0, warn: 0, items: [] },
    runtime: { pass: 0, fail: 0, warn: 0, items: [] }
};

function addResult(category, type, message) {
    const emoji = type === 'pass' ? '✅' : type === 'fail' ? '❌' : '⚠️';
    const fullMessage = `${emoji} ${message}`;
    
    diagnostics[category][type]++;
    diagnostics[category].items.push(fullMessage);
    console.log(fullMessage);
}

// Test 1: Configuration Analysis
console.log('1️⃣  CONFIGURATION ANALYSIS\n');

// Android Manifest
const manifestPath = 'android/app/src/main/AndroidManifest.xml';
if (fs.existsSync(manifestPath)) {
    const manifest = fs.readFileSync(manifestPath, 'utf8');
    
    // Essential permissions
    const permissions = [
        { name: 'INTERNET', required: true },
        { name: 'POST_NOTIFICATIONS', required: false },
        { name: 'com.google.android.c2dm.permission.RECEIVE', required: true }
    ];
    
    permissions.forEach(perm => {
        if (manifest.includes(perm.name)) {
            addResult('configuration', 'pass', `Permission ${perm.name} found`);
        } else {
            addResult('configuration', perm.required ? 'fail' : 'warn', 
                `Missing permission: ${perm.name}`);
        }
    });
    
    // FCM Service
    if (manifest.includes('com.google.firebase.MESSAGING_EVENT')) {
        addResult('configuration', 'pass', 'FCM service intent filter configured');
    } else {
        addResult('configuration', 'fail', 'Missing FCM service intent filter');
    }
    
    // Notification channel
    const channelMatch = manifest.match(/default_notification_channel_id[\s\S]*?value="([^"]+)"/);
    if (channelMatch) {
        addResult('configuration', 'pass', `Notification channel: ${channelMatch[1]}`);
        global.channelId = channelMatch[1];
    } else {
        addResult('configuration', 'warn', 'No default notification channel configured');
    }
    
} else {
    addResult('configuration', 'fail', 'AndroidManifest.xml not found');
}

// Firebase Configuration
const googleServicesPath = 'android/app/google-services.json';
if (fs.existsSync(googleServicesPath)) {
    try {
        const config = JSON.parse(fs.readFileSync(googleServicesPath, 'utf8'));
        addResult('configuration', 'pass', `Firebase project: ${config.project_info.project_id}`);
        
        if (config.client && config.client[0]) {
            const client = config.client[0];
            addResult('configuration', 'pass', `Package: ${client.client_info.android_client_info.package_name}`);
        }
    } catch (e) {
        addResult('configuration', 'fail', 'Invalid google-services.json');
    }
} else {
    addResult('configuration', 'fail', 'google-services.json not found');
}

// Dependencies
const pubspecPath = 'pubspec.yaml';
if (fs.existsSync(pubspecPath)) {
    const pubspec = fs.readFileSync(pubspecPath, 'utf8');
    
    const deps = ['firebase_core', 'firebase_messaging', 'flutter_local_notifications'];
    deps.forEach(dep => {
        if (pubspec.includes(dep)) {
            addResult('configuration', 'pass', `Dependency: ${dep}`);
        } else {
            addResult('configuration', 'fail', `Missing dependency: ${dep}`);
        }
    });
} else {
    addResult('configuration', 'fail', 'pubspec.yaml not found');
}

console.log('\n2️⃣  ENVIRONMENT ANALYSIS\n');

// Check Flutter environment
function checkEnvironment() {
    return new Promise((resolve) => {
        exec('flutter doctor --machine', (error, stdout, stderr) => {
            if (error) {
                addResult('environment', 'warn', 'Flutter doctor check failed');
                resolve();
                return;
            }
            
            try {
                const doctor = JSON.parse(stdout);
                doctor.forEach(item => {
                    if (item.statusHelp === 'No issues found!') {
                        addResult('environment', 'pass', `${item.name}: OK`);
                    } else {
                        addResult('environment', 'warn', `${item.name}: Issues detected`);
                    }
                });
            } catch (e) {
                addResult('environment', 'warn', 'Could not parse flutter doctor output');
            }
            
            resolve();
        });
    });
}

// Check connected devices
function checkDevices() {
    return new Promise((resolve) => {
        exec('flutter devices --machine', (error, stdout, stderr) => {
            if (error) {
                addResult('environment', 'warn', 'Could not check devices');
                resolve();
                return;
            }
            
            try {
                const devices = JSON.parse(stdout);
                const androidDevices = devices.filter(d => d.targetPlatform === 'android');
                
                if (androidDevices.length > 0) {
                    androidDevices.forEach(device => {
                        addResult('environment', 'pass', `Android device: ${device.name}`);
                    });
                } else {
                    addResult('environment', 'warn', 'No Android devices connected');
                }
            } catch (e) {
                addResult('environment', 'warn', 'Could not parse devices output');
            }
            
            resolve();
        });
    });
}

console.log('\n3️⃣  RUNTIME ANALYSIS\n');

// Check FCM service implementation
const fcmServicePath = 'lib/core/services/firebase_messaging_service.dart';
if (fs.existsSync(fcmServicePath)) {
    const service = fs.readFileSync(fcmServicePath, 'utf8');
    
    // Check essential methods
    const methods = [
        'FirebaseMessaging.onMessage.listen',
        'createNotificationChannel',
        'FlutterLocalNotificationsPlugin',
        'getToken'
    ];
    
    methods.forEach(method => {
        if (service.includes(method)) {
            addResult('runtime', 'pass', `Method implemented: ${method}`);
        } else {
            addResult('runtime', 'fail', `Missing method: ${method}`);
        }
    });
    
    // Check channel consistency
    if (global.channelId && service.includes(`'${global.channelId}'`)) {
        addResult('runtime', 'pass', 'Channel ID consistent between manifest and service');
    } else if (global.channelId) {
        addResult('runtime', 'warn', 'Channel ID mismatch between manifest and service');
    }
    
} else {
    addResult('runtime', 'fail', 'FCM service not found');
}

// Check main.dart initialization
const mainPath = 'lib/main.dart';
if (fs.existsSync(mainPath)) {
    const main = fs.readFileSync(mainPath, 'utf8');
    
    if (main.includes('Firebase.initializeApp')) {
        addResult('runtime', 'pass', 'Firebase initialization found');
    } else {
        addResult('runtime', 'fail', 'Missing Firebase initialization');
    }
    
    if (main.includes('FirebaseMessaging.onBackgroundMessage')) {
        addResult('runtime', 'pass', 'Background message handler registered');
    } else {
        addResult('runtime', 'warn', 'Background message handler not found');
    }
} else {
    addResult('runtime', 'fail', 'main.dart not found');
}

async function generateReport() {
    await checkEnvironment();
    await checkDevices();
    
    console.log('\n📊 DIAGNOSTIC SUMMARY');
    console.log('=====================\n');
    
    const categories = ['configuration', 'environment', 'runtime'];
    let totalPass = 0, totalFail = 0, totalWarn = 0;
    
    categories.forEach(cat => {
        const d = diagnostics[cat];
        console.log(`${cat.toUpperCase()}:`);
        console.log(`  ✅ Pass: ${d.pass}`);
        console.log(`  ❌ Fail: ${d.fail}`);
        console.log(`  ⚠️  Warn: ${d.warn}`);
        console.log('');
        
        totalPass += d.pass;
        totalFail += d.fail;
        totalWarn += d.warn;
    });
    
    console.log(`OVERALL: ✅ ${totalPass} | ❌ ${totalFail} | ⚠️ ${totalWarn}\n`);
    
    // Determine status
    let status = 'UNKNOWN';
    let recommendation = '';
    
    if (totalFail === 0 && totalWarn <= 2) {
        status = '🎉 EXCELLENT - FCM should work';
        recommendation = 'Configuration looks good! If notifications still don\'t work, check device settings.';
    } else if (totalFail === 0) {
        status = '✅ GOOD - Minor issues only';
        recommendation = 'Configuration is mostly correct. Address warnings for optimal performance.';
    } else if (totalFail <= 2) {
        status = '⚠️  ISSUES DETECTED';
        recommendation = 'Fix the failed items to enable FCM functionality.';
    } else {
        status = '❌ CRITICAL ISSUES';
        recommendation = 'Multiple critical issues found. FCM will not work until these are fixed.';
    }
    
    console.log(`STATUS: ${status}`);
    console.log(`RECOMMENDATION: ${recommendation}\n`);
    
    // Save detailed report
    const report = {
        timestamp: new Date().toISOString(),
        status: status,
        recommendation: recommendation,
        summary: { pass: totalPass, fail: totalFail, warn: totalWarn },
        details: diagnostics
    };
    
    fs.writeFileSync('fcm_complete_diagnostic.json', JSON.stringify(report, null, 2));
    console.log('📄 Complete diagnostic saved to: fcm_complete_diagnostic.json');
    
    // Next steps
    if (totalFail === 0) {
        console.log('\n🚀 READY TO TEST:');
        console.log('1. Connect Android device: flutter devices');
        console.log('2. Run app: flutter run');
        console.log('3. Tap "🚀 Test FCM" button in dashboard');
        console.log('4. Copy token from console logs');
        console.log('5. Test in Firebase Console > Cloud Messaging');
    } else {
        console.log('\n🔧 FIX REQUIRED:');
        diagnostics.configuration.items.concat(diagnostics.runtime.items)
            .filter(item => item.includes('❌'))
            .forEach(item => console.log(`   ${item}`));
    }
}

generateReport().catch(console.error);
