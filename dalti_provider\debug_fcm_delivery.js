#!/usr/bin/env node

/**
 * Advanced FCM Delivery Debugging
 * Since SHA certificate is configured, let's find the real issue
 */

const fs = require('fs');

console.log('🔬 ADVANCED FCM DELIVERY DEBUGGING');
console.log('===================================\n');

const CURRENT_TOKEN = 'dX8_yF2uRyyVvcN-AmfjFl:APA91bFuCLThVntBVy-OHA_w12zbh1T7XYXcdV5nAKlxC1VRrFYfrMfHc5C3NjnhBIVW2ccv-QIBumqGpX5yMGQcgtuRNXp9RY3dJjK5mWGWKLYH9DtAuIM';

let findings = [];

function addFinding(type, message, solution = null) {
    const emoji = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : '✅';
    findings.push({ type, message, solution, emoji });
    console.log(`${emoji} ${message}`);
    if (solution) console.log(`   💡 Solution: ${solution}`);
}

console.log('1️⃣  DEEP FIREBASE CONFIGURATION ANALYSIS\n');

// Check google-services.json in detail
function analyzeFirebaseConfig() {
    const configPath = 'android/app/google-services.json';
    
    if (!fs.existsSync(configPath)) {
        addFinding('error', 'google-services.json not found', 'Download from Firebase Console');
        return;
    }
    
    try {
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        
        // Check project info
        addFinding('success', `Project ID: ${config.project_info.project_id}`);
        addFinding('success', `Project Number: ${config.project_info.project_number}`);
        
        // Check client configuration
        const client = config.client[0];
        const clientInfo = client.client_info;
        
        addFinding('success', `App ID: ${clientInfo.mobilesdk_app_id}`);
        addFinding('success', `Package: ${clientInfo.android_client_info.package_name}`);
        
        // Check API keys
        if (client.api_key && client.api_key.length > 0) {
            addFinding('success', `API Keys configured: ${client.api_key.length} keys`);
            
            // Check if current API key is restricted
            const currentApiKey = client.api_key[0].current_key;
            if (currentApiKey) {
                addFinding('success', `Current API Key: ${currentApiKey.substring(0, 20)}...`);
            }
        } else {
            addFinding('error', 'No API keys configured', 'Check Firebase Console API settings');
        }
        
        // Check OAuth client (for SHA certificates)
        if (client.oauth_client && client.oauth_client.length > 0) {
            addFinding('success', `OAuth clients: ${client.oauth_client.length}`);
            
            client.oauth_client.forEach((oauth, index) => {
                if (oauth.certificate_hash) {
                    addFinding('success', `SHA certificate ${index + 1}: ${oauth.certificate_hash.substring(0, 20)}...`);
                }
            });
        } else {
            addFinding('warning', 'No OAuth clients found', 'This might be normal for some configurations');
        }
        
        // Check services
        if (client.services) {
            if (client.services.firebase_messaging) {
                const messaging = client.services.firebase_messaging;
                addFinding('success', 'Firebase Messaging service configured');
                
                if (messaging.status === 1) {
                    addFinding('success', 'Firebase Messaging status: ENABLED');
                } else {
                    addFinding('error', `Firebase Messaging status: ${messaging.status}`, 'Enable in Firebase Console');
                }
            } else {
                addFinding('error', 'Firebase Messaging service NOT configured', 'Enable Cloud Messaging in Firebase Console');
            }
        }
        
    } catch (e) {
        addFinding('error', `Invalid google-services.json: ${e.message}`, 'Re-download from Firebase Console');
    }
}

console.log('\n2️⃣  NOTIFICATION CHANNEL DEEP DIVE\n');

// Analyze notification channel configuration
function analyzeNotificationChannels() {
    // Check AndroidManifest.xml
    const manifestPath = 'android/app/src/main/AndroidManifest.xml';
    if (fs.existsSync(manifestPath)) {
        const manifest = fs.readFileSync(manifestPath, 'utf8');
        
        // Extract channel ID from manifest
        const channelMatch = manifest.match(/default_notification_channel_id[\s\S]*?value="([^"]+)"/);
        if (channelMatch) {
            const manifestChannelId = channelMatch[1];
            addFinding('success', `Manifest channel ID: ${manifestChannelId}`);
            
            // Check service implementation
            const servicePath = 'lib/core/services/firebase_messaging_service.dart';
            if (fs.existsSync(servicePath)) {
                const service = fs.readFileSync(servicePath, 'utf8');
                
                // Count occurrences of channel ID in service
                const channelOccurrences = (service.match(new RegExp(`'${manifestChannelId}'`, 'g')) || []).length;
                
                if (channelOccurrences >= 2) {
                    addFinding('success', `Service uses correct channel ID (${channelOccurrences} occurrences)`);
                } else if (channelOccurrences === 1) {
                    addFinding('warning', 'Channel ID found only once in service', 'Check all notification methods use same channel');
                } else {
                    addFinding('error', 'Service does NOT use manifest channel ID', 'Update service to use correct channel ID');
                }
                
                // Check if channel is created
                if (service.includes('createNotificationChannel')) {
                    addFinding('success', 'Notification channel creation implemented');
                } else {
                    addFinding('error', 'Notification channel creation missing', 'Add channel creation in service initialization');
                }
                
                // Check importance level
                if (service.includes('Importance.high')) {
                    addFinding('success', 'High importance notifications configured');
                } else {
                    addFinding('warning', 'Notification importance not set to high', 'Set importance to high for better delivery');
                }
            }
        }
    }
}

console.log('\n3️⃣  FCM MESSAGE HANDLER ANALYSIS\n');

// Check message handlers
function analyzeMessageHandlers() {
    const servicePath = 'lib/core/services/firebase_messaging_service.dart';
    const mainPath = 'lib/main.dart';
    
    if (fs.existsSync(servicePath)) {
        const service = fs.readFileSync(servicePath, 'utf8');
        
        // Check foreground handler
        if (service.includes('FirebaseMessaging.onMessage.listen')) {
            addFinding('success', 'Foreground message handler configured');
            
            // Check if it shows notifications
            if (service.includes('_localNotifications.show')) {
                addFinding('success', 'Foreground handler shows local notifications');
            } else {
                addFinding('error', 'Foreground handler does NOT show notifications', 'Add local notification display in onMessage handler');
            }
        } else {
            addFinding('error', 'Foreground message handler missing', 'Add FirebaseMessaging.onMessage.listen handler');
        }
        
        // Check background handler registration
        if (fs.existsSync(mainPath)) {
            const main = fs.readFileSync(mainPath, 'utf8');
            
            if (main.includes('FirebaseMessaging.onBackgroundMessage')) {
                addFinding('success', 'Background message handler registered in main.dart');
            } else {
                addFinding('error', 'Background message handler NOT registered', 'Add FirebaseMessaging.onBackgroundMessage in main.dart');
            }
        }
    }
}

console.log('\n4️⃣  TOKEN VALIDATION & TESTING\n');

// Advanced token validation
function validateToken() {
    console.log(`🔍 Analyzing token: ${CURRENT_TOKEN.substring(0, 30)}...`);
    
    // Split token parts
    const parts = CURRENT_TOKEN.split(':');
    if (parts.length === 2) {
        addFinding('success', `Token format valid: ${parts[0].length} + ${parts[1].length} characters`);
        
        // Check if token looks like a valid FCM token
        if (parts[0].length > 10 && parts[1].startsWith('APA91b')) {
            addFinding('success', 'Token structure appears valid (APA91b prefix)');
        } else {
            addFinding('warning', 'Token structure unusual', 'Verify this is a real FCM token');
        }
    } else {
        addFinding('error', 'Invalid token format', 'Token should have exactly one colon separator');
    }
    
    // Check token age (approximate)
    const timestamp = Date.now();
    addFinding('success', `Token tested at: ${new Date(timestamp).toISOString()}`);
}

console.log('\n5️⃣  FIREBASE CONSOLE TESTING GUIDE\n');

// Generate specific testing instructions
function generateTestingGuide() {
    console.log('📋 STEP-BY-STEP FIREBASE CONSOLE TEST:');
    console.log('=====================================');
    console.log('');
    console.log('1. Open Firebase Console: https://console.firebase.google.com/');
    console.log('2. Select project: dalti-prod');
    console.log('3. Go to: Messaging > Send your first message');
    console.log('4. Fill in:');
    console.log('   - Title: "FCM Debug Test"');
    console.log('   - Text: "Testing notification delivery"');
    console.log('5. Click "Send test message"');
    console.log('6. Paste this EXACT token:');
    console.log('');
    console.log(`${CURRENT_TOKEN}`);
    console.log('');
    console.log('7. Click "Test"');
    console.log('8. Check your device immediately');
    console.log('');
    console.log('🔍 WHAT TO CHECK:');
    console.log('- Notification appears in notification panel');
    console.log('- App receives the message (check console logs)');
    console.log('- Try with app in foreground AND background');
    console.log('');
    console.log('📱 DEVICE SETTINGS TO VERIFY:');
    console.log('- Settings > Apps > Dalti Provider > Notifications > Allow');
    console.log('- Do Not Disturb is OFF');
    console.log('- Battery optimization disabled for Dalti Provider');
    console.log('- App has notification permission granted');
}

// Run all analyses
analyzeFirebaseConfig();
analyzeNotificationChannels();
analyzeMessageHandlers();
validateToken();
generateTestingGuide();

console.log('\n📊 DEBUGGING SUMMARY');
console.log('====================\n');

const errors = findings.filter(f => f.type === 'error');
const warnings = findings.filter(f => f.type === 'warning');
const successes = findings.filter(f => f.type === 'success');

console.log(`✅ Success: ${successes.length}`);
console.log(`⚠️  Warnings: ${warnings.length}`);
console.log(`❌ Errors: ${errors.length}\n`);

if (errors.length > 0) {
    console.log('🚨 CRITICAL ISSUES TO FIX:');
    errors.forEach(error => {
        console.log(`   ${error.emoji} ${error.message}`);
        if (error.solution) console.log(`      💡 ${error.solution}`);
    });
    console.log('');
}

if (warnings.length > 0) {
    console.log('⚠️  WARNINGS TO ADDRESS:');
    warnings.forEach(warning => {
        console.log(`   ${warning.emoji} ${warning.message}`);
        if (warning.solution) console.log(`      💡 ${warning.solution}`);
    });
    console.log('');
}

// Save report
const report = {
    timestamp: new Date().toISOString(),
    token: CURRENT_TOKEN,
    summary: {
        errors: errors.length,
        warnings: warnings.length,
        successes: successes.length
    },
    findings: findings
};

fs.writeFileSync('fcm_delivery_analysis.json', JSON.stringify(report, null, 2));
console.log('📄 Detailed analysis saved to: fcm_delivery_analysis.json');

if (errors.length === 0) {
    console.log('\n🎉 NO CRITICAL ERRORS FOUND!');
    console.log('Configuration appears correct. Try the Firebase Console test above.');
    console.log('If it still doesn\'t work, the issue might be device-specific settings.');
} else {
    console.log('\n🔧 Fix the critical issues above, then test again.');
}
