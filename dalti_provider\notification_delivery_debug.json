{"timestamp": "2025-09-06T14:36:43.447Z", "token": "dX8_yF2uRyyVvcN-AmfjFl:APA91bFuCLThVntBVy-OHA_w12zbh1T7XYXcdV5nAKlxC1VRrFYfrMfHc5C3NjnhBIVW2ccv-QIBumqGpX5yMGQcgtuRNXp9RY3dJjK5mWGWKLYH9DtAuIM", "summary": {"passed": 6, "failed": 7}, "checks": [{"status": false, "message": "❌ ADB not available - cannot check device state"}, {"status": false, "message": "❌ Cannot check notification settings via ADB"}, {"status": false, "message": "❌ Cannot check Do Not Disturb state"}, {"status": true, "message": "✅ Token length valid: 142 characters"}, {"status": true, "message": "✅ Token format valid (contains colon separator)"}, {"status": true, "message": "✅ Token prefix appears valid"}, {"status": true, "message": "✅ Firebase project: dalti-prod"}, {"status": false, "message": "❌ Firebase Messaging service NOT enabled in project"}, {"status": false, "message": "❌ No OAuth client configured (missing SHA certificates)"}, {"status": true, "message": "✅ Default notification channel: dalti_provider_notifications"}, {"status": true, "message": "✅ Service uses matching notification channel"}, {"status": false, "message": "❌ Cannot send test broadcast to device"}, {"status": false, "message": "❌ Notification system may be disabled"}], "issues": ["ADB not available - cannot check device state", "Cannot check notification settings via ADB", "Cannot check Do Not Disturb state", "Firebase Messaging service NOT enabled in project", "No OAuth client configured (missing SHA certificates)", "Cannot send test broadcast to device", "Notification system may be disabled"]}