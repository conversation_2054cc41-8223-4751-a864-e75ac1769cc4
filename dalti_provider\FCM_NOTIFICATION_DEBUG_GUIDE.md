# FCM Notification Debugging Guide

## 🚨 **Problem**: Getting FCM token but not receiving test notifications from Firebase Console

## 🔍 **Debugging Steps**

### **Step 1: Check App Logs**
After login, look for these logs in your console:

```
[FCM] ===== REQUESTING NOTIFICATION PERMISSIONS =====
[FCM] Permission Status: AuthorizationStatus.authorized
[FCM] ✅ Notifications AUTHORIZED

[FCM] ===== CONFIGURING MESSAGE HANDLERS =====
[FCM] Message handlers configured successfully

[FCM] ===== SEND TOKEN TO SERVER START =====
[FCM] FCM token retrieved successfully: [token_preview]...
```

### **Step 2: Run FCM Debug Method**
Add this to your app after login to get detailed status:

```dart
// Call this after login to debug FCM setup
await FirebaseMessagingService.debugFCMSetup();
```

**Expected Output:**
```
[FCM] ===== DEBUGGING FCM SETUP =====
[FCM] FCM Service Initialized: true
[FCM] Token Sent to Server: true
[FCM] NotificationApiService Available: true
[FCM] ✅ FCM Token Available: [token_preview]...
[FCM] Current Permission Status: AuthorizationStatus.authorized
[FCM] Alert Enabled: SettingsEnabledState.enabled
[FCM] Badge Enabled: SettingsEnabledState.enabled
[FCM] Sound Enabled: SettingsEnabledState.enabled
[FCM] ✅ JWT Token Available: [jwt_preview]...
```

### **Step 3: Check Firebase Console Configuration**

#### **🔥 CRITICAL: Web App ID Issue**
Your current config uses a **temporary web app ID**:
```
appId: '1:1060372851323:web:c968a0882c726c190690de' // TEMPORARY!
```

**This is likely the main issue!** Firebase Console might not recognize this temporary ID.

#### **Fix Required:**
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select project: `dalti-prod`
3. Click "Add app" → "Web"
4. Register your web domain
5. Copy the **real web app ID**
6. Update these files:
   - `lib/firebase_options.dart` (line 46)
   - `web/firebase-messaging-sw.js` (line 10)
   - `build/web/firebase-messaging-sw.js` (line 10)

### **Step 4: Test Different Message Types**

#### **Test 1: Notification Message (Recommended)**
In Firebase Console → Cloud Messaging → Send test message:
```json
{
  "notification": {
    "title": "Test Notification",
    "body": "This is a test message"
  }
}
```

#### **Test 2: Data Message**
```json
{
  "data": {
    "type": "test",
    "message": "Test data message"
  }
}
```

### **Step 5: Platform-Specific Checks**

#### **Android:**
- ✅ Should work immediately (proper google-services.json)
- Check device notification settings
- Ensure app is not in battery optimization

#### **Web:**
- ⚠️ **Main Issue**: Temporary web app ID
- Check browser notification permissions
- Open browser dev tools → Console for errors
- Check if service worker is registered

#### **iOS:**
- ⚠️ Needs proper iOS app registration in Firebase
- Requires APNs certificate setup

### **Step 6: Check Message Reception Logs**

When a notification is sent, you should see:

**Foreground (app open):**
```
[FCM] ===== FOREGROUND MESSAGE RECEIVED =====
[FCM] Message ID: [message_id]
[FCM] Notification Title: Test Notification
[FCM] Notification Body: This is a test message
[FCM] ===== HANDLING FOREGROUND MESSAGE =====
[FCM] Platform: MOBILE/WEB
[FCM] Showing local notification...
```

**Background:**
Check service worker logs in browser dev tools.

## 🛠️ **Most Likely Solutions**

### **1. Register Real Web App (HIGH PRIORITY)**
```bash
# Current temporary ID causing issues
appId: '1:1060372851323:web:c968a0882c726c190690de'

# Need real web app ID from Firebase Console
appId: '1:1060372851323:web:REAL_WEB_APP_ID_HERE'
```

### **2. Check Notification Permissions**
```dart
// Check if permissions are granted
final settings = await FirebaseMessaging.instance.getNotificationSettings();
if (settings.authorizationStatus != AuthorizationStatus.authorized) {
  // Request permissions again
  await FirebaseMessaging.instance.requestPermission();
}
```

### **3. Test on Android First**
Android should work immediately since it has proper configuration. If Android doesn't work, there's a deeper issue.

### **4. Check Browser Console (Web)**
Open browser dev tools and look for:
- Service worker registration errors
- Firebase initialization errors
- Permission denied errors

## 📋 **Quick Checklist**

- [ ] App shows "Notifications AUTHORIZED" in logs
- [ ] FCM token is generated and sent to server
- [ ] Real web app ID registered in Firebase Console (not temporary)
- [ ] Message handlers are configured
- [ ] Browser/device notification permissions granted
- [ ] Testing with simple notification message (not data-only)
- [ ] Service worker properly registered (web)

## 🚀 **Next Steps**

1. **Register real web app** in Firebase Console (highest priority)
2. **Test on Android** first (should work immediately)
3. **Run debug method** after login to check status
4. **Check browser console** for web-specific errors
5. **Try different message types** in Firebase Console

The temporary web app ID is most likely the root cause of the issue!
